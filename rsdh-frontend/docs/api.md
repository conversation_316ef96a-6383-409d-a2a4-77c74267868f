# 项目API文档

本文档详细描述了项目的后端API接口规范。

## 目录
- [1. 认证相关](#1-认证相关)
  - [1.1 发送验证码](#11-发送验证码)
  - [1.2 登录/注册](#12-登录注册)
- [2. 用户相关](#2-用户相关)
  - [2.1 获取当前用户信息](#21-获取当前用户信息)
  - [2.2 更新用户信息](#22-更新用户信息)
- [3. 导师相关](#3-导师相关)
  - [3.1 获取导师列表](#31-获取导师列表)
  - [3.2 获取导师详情](#32-获取导师详情)
  - [3.3 获取导师可预约时间](#33-获取导师可预约时间)
  - [3.4 提交导师申请](#34-提交导师申请)
- [4. 预约相关](#4-预约相关)
  - [4.1 创建预约](#41-创建预约)
  - [4.2 获取预约列表](#42-获取预约列表)
  - [4.3 获取预约详情](#43-获取预约详情)
  - [4.4 更新预约状态](#44-更新预约状态)
- [5. 评价相关](#5-评价相关)
  - [5.1 提交评价](#51-提交评价)
  - [5.2 获取导师评价列表](#52-获取导师评价列表)
- [6. 反馈相关](#6-反馈相关)
  - [6.1 提交反馈](#61-提交反馈)
- [7. 系统相关](#7-系统相关)
  - [7.1 获取系统配置](#71-获取系统配置)
- [接口规范](#接口规范)
  - [错误处理](#错误处理)
  - [分页](#分页)
  - [认证](#认证)
  - [文件上传](#文件上传)

## 1. 认证相关

### 1.1 发送验证码
- **URL**: `/api/auth/send-code`
- **方法**: POST
- **请求体**:
  ```json
  {
    "phone": "13812345678"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "验证码已发送"
  }
  ```

### 1.2 登录/注册
- **URL**: `/api/auth/login`
- **方法**: POST
- **请求体**:
  ```json
  {
    "phone": "13812345678",
    "code": "123456"
  }
  ```
- **响应**:
  ```json
  {
    "token": "jwt.token.here",
    "expiresIn": 86400,
    "user": {
      "id": "user_123",
      "name": "张同学",
      "phone": "13812345678",
      "role": "student",
      "avatar": "👦"
    }
  }
  ```

## 2. 用户相关

### 2.1 获取当前用户信息
- **URL**: `/api/users/me`
- **方法**: GET
- **Headers**:
  - `Authorization: Bearer <token>`
- **响应**:
  ```json
  {
    "id": "user_123",
    "name": "张同学",
    "phone": "13812345678",
    "role": "student",
    "avatar": "👦",
    "email": "<EMAIL>",
    "bio": "我是一名大学生，正在寻找职业发展建议"
  }
  ```

### 2.2 更新用户信息
- **URL**: `/api/users/me`
- **方法**: PUT
- **Headers**:
  - `Authorization: Bearer <token>`
  - `Content-Type: multipart/form-data`
- **请求体**:
  - `name`: 姓名 (可选)
  - `email`: 邮箱 (可选)
  - `bio`: 个人简介 (可选)
  - `avatar`: 头像文件 (可选)
- **响应**:
  ```json
  {
    "success": true,
    "message": "用户信息更新成功"
  }
  ```

## 3. 导师相关

### 3.1 获取导师列表
- **URL**: `/api/tutors`
- **方法**: GET
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `pageSize`: 每页数量 (默认: 10)
  - `industry`: 行业ID (可选)
  - `specialty`: 专长ID (可选)
  - `university`: 毕业院校ID (可选)
  - `minRating`: 最低评分 (可选)
  - `search`: 搜索关键词 (可选)
- **响应**:
  ```json
  {
    "items": [
      {
        "id": "tutor_123",
        "name": "李老师",
        "title": "高级职业规划师",
        "company": "某知名互联网公司",
        "position": "高级产品经理",
        "industry": "互联网/IT",
        "specialties": ["产品设计", "职业规划", "简历优化"],
        "avatar": "https://example.com/avatars/li.jpg",
        "rating": 4.8,
        "reviewCount": 128,
        "price": 299,
        "description": "5年互联网产品经验，擅长职业规划与简历优化..."
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
  ```

### 3.2 获取导师详情
- **URL**: `/api/tutors/{tutorId}`
- **方法**: GET
- **URL参数**:
  - `tutorId`: 导师ID
- **响应**:
  ```json
  {
    "id": "tutor_123",
    "name": "李老师",
    "title": "高级职业规划师",
    "company": "某知名互联网公司",
    "position": "高级产品经理",
    "industry": "互联网/IT",
    "specialties": ["产品设计", "职业规划", "简历优化"],
    "avatar": "https://example.com/avatars/li.jpg",
    "rating": 4.8,
    "reviewCount": 128,
    "price": 299,
    "description": "5年互联网产品经验，擅长职业规划与简历优化...",
    "education": [
      {
        "university": "清华大学",
        "degree": "硕士",
        "major": "计算机科学与技术",
        "year": 2015
      }
    ],
    "experience": [
      {
        "company": "某知名互联网公司",
        "position": "高级产品经理",
        "duration": "2018年至今"
      }
    ],
    "introduction": "我是一名资深产品经理，拥有丰富的职业规划经验...",
    "achievements": "帮助500+学员成功找到理想工作..."
  }
  ```

### 3.3 获取导师可预约时间
- **URL**: `/api/tutors/{tutorId}/availability`
- **方法**: GET
- **URL参数**:
  - `tutorId`: 导师ID
- **查询参数**:
  - `startDate`: 开始日期 (YYYY-MM-DD)
  - `endDate`: 结束日期 (YYYY-MM-DD)
- **响应**:
  ```json
  {
    "availableSlots": [
      {
        "date": "2023-12-01",
        "timeSlots": ["09:00", "10:00", "14:00"]
      },
      {
        "date": "2023-12-02",
        "timeSlots": ["10:00", "11:00", "15:00"]
      }
    ]
  }
  ```

### 3.4 提交导师申请
- **URL**: `/api/tutors/apply`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer <token>`
  - `Content-Type: multipart/form-data`
- **请求体**:
  - `name`: 姓名 (必填)
  - `phone`: 手机号 (必填)
  - `email`: 邮箱 (必填)
  - `university`: 毕业院校 (必填)
  - `major`: 专业 (必填)
  - `degree`: 学历 (必填)
  - `industry`: 行业 (必填)
  - `position`: 职位 (必填)
  - `company`: 公司 (必填)
  - `workYears`: 工作年限 (必填)
  - `specialties`: 专长 (逗号分隔) (必填)
  - `introduction`: 个人介绍 (必填)
  - `resume`: 简历文件 (PDF/DOC/DOCX) (必填)
  - `idCard`: 身份证照片 (正反面) (必填)
  - `diploma`: 学历证书 (必填)
  - `otherCertificates`: 其他证书 (可选)
- **响应**:
  ```json
  {
    "success": true,
    "message": "导师申请提交成功，请等待审核"
  }
  ```

## 4. 预约相关

### 4.1 创建预约
- **URL**: `/api/appointments`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer <token>`
- **请求体**:
  ```json
  {
    "tutorId": "tutor_123",
    "appointmentDate": "2023-12-01",
    "timeSlot": "14:00",
    "meetingType": "video",
    "notes": "我想咨询职业规划问题"
  }
  ```
- **响应**:
  ```json
  {
    "id": "appt_123",
    "tutorId": "tutor_123",
    "studentId": "user_123",
    "appointmentDate": "2023-12-01",
    "timeSlot": "14:00",
    "meetingType": "video",
    "status": "pending",
    "notes": "我想咨询职业规划问题",
    "meetingLink": "https://meet.example.com/abc123",
    "createdAt": "2023-11-28T10:00:00Z"
  }
  ```

### 4.2 获取预约列表
- **URL**: `/api/appointments`
- **方法**: GET
- **Headers**:
  - `Authorization: Bearer <token>`
- **查询参数**:
  - `status`: 状态 (pending/confirmed/completed/cancelled) (可选)
  - `role`: 角色 (student/tutor) (可选, 默认为student)
  - `page`: 页码 (默认: 1)
  - `pageSize`: 每页数量 (默认: 10)
- **响应**:
  ```json
  {
    "items": [
      {
        "id": "appt_123",
        "tutor": {
          "id": "tutor_123",
          "name": "李老师",
          "avatar": "https://example.com/avatars/li.jpg"
        },
        "student": {
          "id": "user_123",
          "name": "张同学",
          "avatar": "👦"
        },
        "appointmentDate": "2023-12-01",
        "timeSlot": "14:00",
        "meetingType": "video",
        "status": "confirmed",
        "meetingLink": "https://meet.example.com/abc123",
        "createdAt": "2023-11-28T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10
  }
  ```

### 4.3 获取预约详情
- **URL**: `/api/appointments/{appointmentId}`
- **方法**: GET
- **Headers**:
  - `Authorization: Bearer <token>`
- **URL参数**:
  - `appointmentId`: 预约ID
- **响应**:
  ```json
  {
    "id": "appt_123",
    "tutor": {
      "id": "tutor_123",
      "name": "李老师",
      "avatar": "https://example.com/avatars/li.jpg",
      "title": "高级职业规划师",
      "company": "某知名互联网公司"
    },
    "student": {
      "id": "user_123",
      "name": "张同学",
      "avatar": "👦",
      "phone": "138****5678"
    },
    "appointmentDate": "2023-12-01",
    "timeSlot": "14:00",
    "meetingType": "video",
    "status": "confirmed",
    "notes": "我想咨询职业规划问题",
    "meetingLink": "https://meet.example.com/abc123",
    "createdAt": "2023-11-28T10:00:00Z",
    "updatedAt": "2023-11-28T10:05:00Z"
  }
  ```

### 4.4 更新预约状态
- **URL**: `/api/appointments/{appointmentId}/status`
- **方法**: PATCH
- **Headers**:
  - `Authorization: Bearer <token>`
- **URL参数**:
  - `appointmentId`: 预约ID
- **请求体**:
  ```json
  {
    "status": "cancelled",
    "reason": "临时有事"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "预约状态更新成功"
  }
  ```

## 5. 评价相关

### 5.1 提交评价
- **URL**: `/api/reviews`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer <token>`
- **请求体**:
  ```json
  {
    "appointmentId": "appt_123",
    "rating": 5,
    "content": "李老师非常专业，给出了很多有用的建议！",
    "tags": ["专业", "耐心", "有帮助"]
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "评价提交成功"
  }
  ```

### 5.2 获取导师评价列表
- **URL**: `/api/tutors/{tutorId}/reviews`
- **方法**: GET
- **URL参数**:
  - `tutorId`: 导师ID
- **查询参数**:
  - `page`: 页码 (默认: 1)
  - `pageSize`: 每页数量 (默认: 10)
- **响应**:
  ```json
  {
    "items": [
      {
        "id": "review_123",
        "student": {
          "name": "张同学",
          "avatar": "👦"
        },
        "rating": 5,
        "content": "李老师非常专业，给出了很多有用的建议！",
        "tags": ["专业", "耐心", "有帮助"],
        "createdAt": "2023-11-28T15:30:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 10,
    "averageRating": 4.8,
    "totalReviews": 128
  }
  ```

## 6. 反馈相关

### 6.1 提交反馈
- **URL**: `/api/feedback`
- **方法**: POST
- **Headers**:
  - `Authorization: Bearer <token>`
- **请求体**:
  ```json
  {
    "type": "suggestion",
    "content": "建议增加更多行业导师",
    "contact": "13812345678"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "反馈提交成功"
  }
  ```

## 7. 系统相关

### 7.1 获取系统配置
- **URL**: `/api/system/config`
- **方法**: GET
- **响应**:
  ```json
  {
    "provinces": [
      {"id": 1, "name": "北京市"},
      {"id": 2, "name": "上海市"},
      // ...
    ],
    "universities": [
      {"id": 1, "name": "清华大学", "provinceId": 1},
      {"id": 2, "name": "北京大学", "provinceId": 1},
      // ...
    ],
    "majors": [
      {"id": 1, "name": "计算机科学与技术"},
      {"id": 2, "name": "软件工程"},
      // ...
    ],
    "industries": [
      {"id": 1, "name": "互联网/IT"},
      {"id": 2, "name": "金融"},
      // ...
    ],
    "specialties": [
      {"id": 1, "name": "职业规划", "industryId": 1},
      {"id": 2, "name": "简历优化", "industryId": 1},
      // ...
    ]
  }
  ```

## 接口规范

### 错误处理
所有错误响应都遵循以下格式：
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  }
}
```

常见错误码：
- `AUTH_REQUIRED`: 需要登录
- `INVALID_TOKEN`: 无效的token
- `PERMISSION_DENIED`: 权限不足
- `VALIDATION_ERROR`: 参数验证失败
- `RESOURCE_NOT_FOUND`: 资源不存在
- `INTERNAL_SERVER_ERROR`: 服务器内部错误

### 分页
所有分页接口返回格式：
```json
{
  "items": [],
  "total": 100,
  "page": 1,
  "pageSize": 10
}
```

### 认证
需要认证的接口需在请求头中添加：
```
Authorization: Bearer <token>
```

### 文件上传
文件上传接口使用 `multipart/form-data` 格式，支持以下文件类型：
- 图片：jpg, jpeg, png, gif (最大5MB)
- 文档：pdf, doc, docx (最大10MB)

文件上传成功后会返回文件URL，请保存此URL用于后续请求。

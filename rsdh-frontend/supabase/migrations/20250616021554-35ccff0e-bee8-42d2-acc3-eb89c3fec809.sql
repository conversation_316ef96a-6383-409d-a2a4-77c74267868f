
-- 创建用户档案表
CREATE TABLE public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  phone VARCHAR(15),
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建导师表
CREATE TABLE public.tutors (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  university VARCHAR(200) NOT NULL,
  major VARCHAR(200) NOT NULL,
  graduation_year INTEGER NOT NULL,
  current_job VARCHAR(200) NOT NULL,
  company VARCHAR(200) NOT NULL,
  province VARCHAR(50) NOT NULL,
  gaokao_score INTEGER,
  gaokao_province VARCHAR(50),
  bio TEXT,
  experience TEXT,
  specialties TEXT[],
  tags TEXT[],
  price INTEGER NOT NULL DEFAULT 299,
  rating DECIMAL(3,2) DEFAULT 0.0,
  review_count INTEGER DEFAULT 0,
  consultation_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建导师教育经历表
CREATE TABLE public.tutor_education (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tutor_id UUID REFERENCES public.tutors(id) ON DELETE CASCADE,
  degree VARCHAR(50) NOT NULL,
  school VARCHAR(200) NOT NULL,
  major VARCHAR(200) NOT NULL,
  start_year INTEGER NOT NULL,
  end_year INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建导师职业经历表
CREATE TABLE public.tutor_career (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tutor_id UUID REFERENCES public.tutors(id) ON DELETE CASCADE,
  company VARCHAR(200) NOT NULL,
  position VARCHAR(200) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  is_current BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建预约表
CREATE TABLE public.appointments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tutor_id UUID REFERENCES public.tutors(id) ON DELETE CASCADE,
  appointment_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  duration INTEGER NOT NULL DEFAULT 60, -- 分钟
  price INTEGER NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'completed', 'cancelled')),
  meeting_type VARCHAR(20) DEFAULT 'video' CHECK (meeting_type IN ('video', 'voice', 'offline')),
  location TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建评价表
CREATE TABLE public.reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  appointment_id UUID REFERENCES public.appointments(id) ON DELETE CASCADE,
  student_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  tutor_id UUID REFERENCES public.tutors(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  content TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建导师可预约时间表
CREATE TABLE public.tutor_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tutor_id UUID REFERENCES public.tutors(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_available BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用行级安全策略
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_education ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_career ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tutor_availability ENABLE ROW LEVEL SECURITY;

-- 用户档案策略
CREATE POLICY "Users can view all profiles" ON public.profiles FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- 导师策略
CREATE POLICY "Anyone can view active tutors" ON public.tutors FOR SELECT USING (is_active = true);
CREATE POLICY "Tutors can update own profile" ON public.tutors FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can become tutors" ON public.tutors FOR INSERT WITH CHECK (auth.uid() = user_id);

-- 导师教育经历策略
CREATE POLICY "Anyone can view tutor education" ON public.tutor_education FOR SELECT USING (true);
CREATE POLICY "Tutors can manage own education" ON public.tutor_education 
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.tutors 
      WHERE tutors.id = tutor_education.tutor_id 
      AND tutors.user_id = auth.uid()
    )
  );

-- 导师职业经历策略
CREATE POLICY "Anyone can view tutor career" ON public.tutor_career FOR SELECT USING (true);
CREATE POLICY "Tutors can manage own career" ON public.tutor_career 
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.tutors 
      WHERE tutors.id = tutor_career.tutor_id 
      AND tutors.user_id = auth.uid()
    )
  );

-- 预约策略
CREATE POLICY "Users can view own appointments" ON public.appointments 
  FOR SELECT USING (auth.uid() = student_id OR 
    EXISTS (
      SELECT 1 FROM public.tutors 
      WHERE tutors.id = appointments.tutor_id 
      AND tutors.user_id = auth.uid()
    )
  );
CREATE POLICY "Students can create appointments" ON public.appointments 
  FOR INSERT WITH CHECK (auth.uid() = student_id);
CREATE POLICY "Users can update own appointments" ON public.appointments 
  FOR UPDATE USING (auth.uid() = student_id OR 
    EXISTS (
      SELECT 1 FROM public.tutors 
      WHERE tutors.id = appointments.tutor_id 
      AND tutors.user_id = auth.uid()
    )
  );

-- 评价策略
CREATE POLICY "Anyone can view reviews" ON public.reviews FOR SELECT USING (true);
CREATE POLICY "Students can create reviews for their appointments" ON public.reviews 
  FOR INSERT WITH CHECK (
    auth.uid() = student_id AND 
    EXISTS (
      SELECT 1 FROM public.appointments 
      WHERE appointments.id = reviews.appointment_id 
      AND appointments.student_id = auth.uid()
      AND appointments.status = 'completed'
    )
  );

-- 导师可预约时间策略
CREATE POLICY "Anyone can view tutor availability" ON public.tutor_availability FOR SELECT USING (true);
CREATE POLICY "Tutors can manage own availability" ON public.tutor_availability 
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.tutors 
      WHERE tutors.id = tutor_availability.tutor_id 
      AND tutors.user_id = auth.uid()
    )
  );

-- 创建触发器函数来自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为相关表添加更新时间戳触发器
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tutors_updated_at BEFORE UPDATE ON public.tutors 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON public.appointments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入一些示例导师数据
INSERT INTO public.tutors (name, university, major, graduation_year, current_job, company, province, gaokao_score, gaokao_province, bio, experience, specialties, tags, price, rating, review_count, consultation_count) VALUES
('李明', '清华大学', '计算机科学与技术', 2018, '高级软件工程师', '腾讯', '北京', 685, '河南', '我是清华大学计算机系毕业生，目前在腾讯担任高级软件工程师，专注于人工智能和机器学习领域。我曾经历过高考的洗礼，深知选择专业和规划人生的重要性。希望能够帮助更多的同学找到适合自己的发展道路。', '5年互联网行业经验，参与过多个大型项目开发', ARRAY['专业选择指导', '职业规划咨询', '学科竞赛辅导', 'AI领域深度解析'], ARRAY['AI方向', '算法竞赛', '职业规划'], 299, 4.9, 128, 245),
('张雯', '北京协和医学院', '临床医学', 2019, '主治医师', '协和医院', '北京', 678, '山东', '北京协和医学院临床医学专业毕业，现任协和医院主治医师。有丰富的临床经验和医学教育背景，可以为对医学专业感兴趣的同学提供专业指导。', '临床医学5年经验，擅长内科诊疗', ARRAY['医学专业指导', '临床经验分享', '考研指导'], ARRAY['医学专业', '临床经验', '考研指导'], 399, 4.8, 89, 167),
('王强', '上海交通大学', '金融学', 2017, '投资经理', '中信证券', '上海', 672, '江苏', '上海交通大学金融学专业毕业，现任中信证券投资经理。对金融行业有深入理解，可以为想要进入金融领域的同学提供实用建议。', '金融行业6年经验，专注投资分析', ARRAY['金融行业分析', '投资理财指导', '职业发展规划'], ARRAY['金融分析', '投资理财', '行业洞察'], 349, 4.7, 156, 203);

-- 为示例导师添加教育经历
INSERT INTO public.tutor_education (tutor_id, degree, school, major, start_year, end_year) 
SELECT id, '本科', university, major, graduation_year-4, graduation_year FROM public.tutors WHERE name = '李明';

INSERT INTO public.tutor_education (tutor_id, degree, school, major, start_year, end_year) 
SELECT id, '硕士', '清华大学', '人工智能', graduation_year, graduation_year+2 FROM public.tutors WHERE name = '李明';

-- 为示例导师添加职业经历
INSERT INTO public.tutor_career (tutor_id, company, position, start_date, is_current) 
SELECT id, company, current_job, '2020-01-01', true FROM public.tutors WHERE name = '李明';

INSERT INTO public.tutor_career (tutor_id, company, position, start_date, end_date, is_current) 
SELECT id, '字节跳动', '软件工程师', '2019-01-01', '2019-12-31', false FROM public.tutors WHERE name = '李明';

-- 添加一些可预约时间示例
INSERT INTO public.tutor_availability (tutor_id, date, start_time, end_time) 
SELECT id, CURRENT_DATE + INTERVAL '1 day', '14:00', '15:00' FROM public.tutors WHERE name = '李明';

INSERT INTO public.tutor_availability (tutor_id, date, start_time, end_time) 
SELECT id, CURRENT_DATE + INTERVAL '1 day', '15:00', '16:00' FROM public.tutors WHERE name = '李明';

INSERT INTO public.tutor_availability (tutor_id, date, start_time, end_time) 
SELECT id, CURRENT_DATE + INTERVAL '2 days', '14:00', '15:00' FROM public.tutors WHERE name = '李明';

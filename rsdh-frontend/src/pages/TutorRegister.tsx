import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Upload, CheckCircle } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useTutorRegister } from "@/hooks/useTutorRegister";
import { toast } from 'sonner';

interface TutorForm {
  name: string;
  phone: string;
  email: string;
  gaokaoScore: string;
  gaokaoYear: string;
  university: string;
  major: string;
  degree: string;
  graduationYear: string;
  company: string;
  position: string;
  workYears: string;
  industry: string;
  specialization: string;
  introduction: string;
  xuexinScreenshot: File | null;
  workProof: File | null;
}

const TutorRegister = () => {
  const navigate = useNavigate();
  const { mutate: submitRegistration, isPending } = useTutorRegister();

  const [form, setForm] = useState<TutorForm>({
    name: '',
    phone: '',
    email: '',
    gaokaoScore: '',
    gaokaoYear: '',
    university: '',
    major: '',
    degree: '',
    graduationYear: '',
    company: '',
    position: '',
    workYears: '',
    industry: '',
    specialization: '',
    introduction: '',
    xuexinScreenshot: null,
    workProof: null
  });

  const [uploadStatus, setUploadStatus] = useState({
    xuexin: false,
    work: false
  });

  const handleInputChange = (field: keyof TutorForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (field: 'xuexinScreenshot' | 'workProof', event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setForm(prev => ({ ...prev, [field]: file }));
      setUploadStatus(prev => ({ 
        ...prev, 
        [field === 'xuexinScreenshot' ? 'xuexin' : 'work']: true 
      }));
      toast.success(`${field === 'xuexinScreenshot' ? '学信网截图' : '在职证明'}上传成功`);
    }
  };

  const handleSubmit = () => {
    // 验证必填字段
    if (!form.name || !form.university || !form.major || !form.graduationYear || !form.company || !form.position) {
      toast.error('请填写所有必填项');
      return;
    }

    if (!uploadStatus.xuexin || !uploadStatus.work) {
      toast.error('请上传学信网截图和在职证明');
      return;
    }

    // 提交数据
    submitRegistration(form, {
      onSuccess: () => {
        setTimeout(() => {
          navigate('/');
        }, 2000);
      }
    });
  };

  const specializations = [
    '专业选择指导', '职业规划咨询', '学科竞争力提升', 
    '留学申请指导', '考研规划', '就业指导'
  ];

  const industries = [
    '互联网/科技', '金融/投资', '医疗/生物', '教育/学术', 
    '制造业', '咨询/服务', '媒体/文化', '政府/公共部门'
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-md mx-auto flex items-center">
          <Link to="/" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">成为导师</h1>
        </div>
      </div>

      <div className="max-w-md mx-auto p-4 pb-20">
        <Card className="border-0 shadow-sm mb-4">
          <CardHeader>
            <CardTitle className="text-center text-lg">导师认证申请</CardTitle>
            <p className="text-center text-sm text-gray-600">
              我们将严格审核您的资质，确保为学生提供专业指导
            </p>
          </CardHeader>
        </Card>

        {/* 基本信息 */}
        <Card className="border-0 shadow-sm mb-4">
          <CardHeader>
            <CardTitle className="text-base">基本信息</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">真实姓名 *</Label>
              <Input
                id="name"
                value={form.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="请输入真实姓名"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="phone">手机号码 *</Label>
              <Input
                id="phone"
                type="tel"
                value={form.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="请输入手机号码"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="email">邮箱地址</Label>
              <Input
                id="email"
                type="email"
                value={form.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="请输入邮箱地址"
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>

        {/* 教育背景 */}
        <Card className="border-0 shadow-sm mb-4">
          <CardHeader>
            <CardTitle className="text-base">教育背景</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="gaokaoScore">高考分数 *</Label>
                <Input
                  id="gaokaoScore"
                  value={form.gaokaoScore}
                  onChange={(e) => handleInputChange('gaokaoScore', e.target.value)}
                  placeholder="如：618"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="gaokaoYear">高考年份 *</Label>
                <Input
                  id="gaokaoYear"
                  value={form.gaokaoYear}
                  onChange={(e) => handleInputChange('gaokaoYear', e.target.value)}
                  placeholder="如：2015"
                  className="mt-1"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="university">毕业院校 *</Label>
              <Input
                id="university"
                value={form.university}
                onChange={(e) => handleInputChange('university', e.target.value)}
                placeholder="如：清华大学"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="major">专业 *</Label>
              <Input
                id="major"
                value={form.major}
                onChange={(e) => handleInputChange('major', e.target.value)}
                placeholder="如：计算机科学与技术"
                className="mt-1"
              />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="degree">学历 *</Label>
                <select
                  id="degree"
                  value={form.degree}
                  onChange={(e) => handleInputChange('degree', e.target.value)}
                  className="w-full h-10 px-3 border border-gray-200 rounded-md mt-1"
                >
                  <option value="">请选择</option>
                  <option value="本科">本科</option>
                  <option value="硕士">硕士</option>
                  <option value="博士">博士</option>
                </select>
              </div>
              <div>
                <Label htmlFor="graduationYear">毕业年份 *</Label>
                <Input
                  id="graduationYear"
                  value={form.graduationYear}
                  onChange={(e) => handleInputChange('graduationYear', e.target.value)}
                  placeholder="如：2019"
                  className="mt-1"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 工作经历 */}
        <Card className="border-0 shadow-sm mb-4">
          <CardHeader>
            <CardTitle className="text-base">工作经历</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="company">当前公司 *</Label>
              <Input
                id="company"
                value={form.company}
                onChange={(e) => handleInputChange('company', e.target.value)}
                placeholder="如：腾讯科技"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="position">职位 *</Label>
              <Input
                id="position"
                value={form.position}
                onChange={(e) => handleInputChange('position', e.target.value)}
                placeholder="如：高级工程师"
                className="mt-1"
              />
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="workYears">工作年限 *</Label>
                <select
                  id="workYears"
                  value={form.workYears}
                  onChange={(e) => handleInputChange('workYears', e.target.value)}
                  className="w-full h-10 px-3 border border-gray-200 rounded-md mt-1"
                >
                  <option value="">请选择</option>
                  <option value="1-3年">1-3年</option>
                  <option value="3-5年">3-5年</option>
                  <option value="5-10年">5-10年</option>
                  <option value="10年以上">10年以上</option>
                </select>
              </div>
              <div>
                <Label htmlFor="industry">所属行业 *</Label>
                <select
                  id="industry"
                  value={form.industry}
                  onChange={(e) => handleInputChange('industry', e.target.value)}
                  className="w-full h-10 px-3 border border-gray-200 rounded-md mt-1"
                >
                  <option value="">请选择</option>
                  {industries.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 辅导专长 */}
        <Card className="border-0 shadow-sm mb-4">
          <CardHeader>
            <CardTitle className="text-base">辅导专长</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="specialization">擅长领域 *</Label>
              <select
                id="specialization"
                value={form.specialization}
                onChange={(e) => handleInputChange('specialization', e.target.value)}
                className="w-full h-10 px-3 border border-gray-200 rounded-md mt-1"
              >
                <option value="">请选择</option>
                {specializations.map(spec => (
                  <option key={spec} value={spec}>{spec}</option>
                ))}
              </select>
            </div>
            <div>
              <Label htmlFor="introduction">辅导宣言 *</Label>
              <Textarea
                id="introduction"
                value={form.introduction}
                onChange={(e) => handleInputChange('introduction', e.target.value)}
                placeholder="请用200字说明您能为学生提供的价值和帮助..."
                rows={4}
                maxLength={200}
                className="mt-1"
              />
              <p className="text-xs text-gray-500 mt-1">
                {form.introduction.length}/200
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 资质证明 */}
        <Card className="border-0 shadow-sm mb-4">
          <CardHeader>
            <CardTitle className="text-base">资质证明</CardTitle>
            <p className="text-sm text-gray-600">请上传相关证明材料</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="block mb-2">学信网学历截图 *</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileUpload('xuexinScreenshot', e)}
                  className="hidden"
                  id="xuexin-upload"
                />
                <label htmlFor="xuexin-upload" className="cursor-pointer">
                  {uploadStatus.xuexin ? (
                    <div className="text-green-500">
                      <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">学信网截图已上传</p>
                    </div>
                  ) : (
                    <div className="text-gray-500">
                      <Upload className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">点击上传学信网学历证明截图</p>
                      <p className="text-xs text-gray-400 mt-1">支持 JPG、PNG 格式</p>
                    </div>
                  )}
                </label>
              </div>
            </div>

            <div>
              <Label className="block mb-2">在职证明 *</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFileUpload('workProof', e)}
                  className="hidden"
                  id="work-upload"
                />
                <label htmlFor="work-upload" className="cursor-pointer">
                  {uploadStatus.work ? (
                    <div className="text-green-500">
                      <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">在职证明已上传</p>
                    </div>
                  ) : (
                    <div className="text-gray-500">
                      <Upload className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">上传工牌、名片等在职证明</p>
                      <p className="text-xs text-gray-400 mt-1">支持 JPG、PNG 格式</p>
                    </div>
                  )}
                </label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 提交按钮 */}
        <div className="space-y-3">
          <Button 
            onClick={handleSubmit}
            className="w-full h-12"
            disabled={isPending || !form.name || !form.university || !uploadStatus.xuexin || !uploadStatus.work}
          >
            {isPending ? '提交中...' : '提交申请'}
          </Button>
          <div className="text-center">
            <p className="text-xs text-gray-500">
              提交后我们将在3个工作日内完成审核
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TutorRegister;

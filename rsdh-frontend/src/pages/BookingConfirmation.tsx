
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Calendar, Clock, MapPin, Video, MessageSquare, User, CheckCircle } from "lucide-react";
import { Link, useSearchParams, useNavigate } from "react-router-dom";
import { useTutorDetails } from "@/hooks/useTutorDetails";
import { useCreateAppointment } from "@/hooks/useCreateAppointment";

// 生成有效的 UUID
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
};

// 生成或获取当前用户ID
const getCurrentUserId = () => {
  let userId = localStorage.getItem('current_user_id');
  if (!userId) {
    userId = generateUUID();
    localStorage.setItem('current_user_id', userId);
  }
  return userId;
};

const BookingConfirmation = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const tutorId = searchParams.get('tutorId');
  const selectedDate = searchParams.get('date');
  const selectedTime = searchParams.get('time');
  const meetingType = searchParams.get('meetingType') || 'video';
  
  const [notes, setNotes] = useState('');
  const [location, setLocation] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);

  const { data, isLoading } = useTutorDetails(tutorId || '');
  const createAppointment = useCreateAppointment();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (!data?.tutor || !selectedDate || !selectedTime) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 text-6xl mb-4">😕</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">预约信息不完整</h3>
          <p className="text-gray-500 mb-4">请返回重新选择时间</p>
          <Link to={`/tutor-profile?id=${tutorId}`}>
            <Button>返回导师页面</Button>
          </Link>
        </div>
      </div>
    );
  }

  const { tutor } = data;
  const [startTime, endTime] = selectedTime.split('-');
  const duration = 60; // 默认60分钟

  const getMeetingTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4" />;
      case 'voice': return <MessageSquare className="h-4 w-4" />;
      case 'offline': return <MapPin className="h-4 w-4" />;
      default: return <Video className="h-4 w-4" />;
    }
  };

  const getMeetingTypeText = (type: string) => {
    switch (type) {
      case 'video': return '视频咨询';
      case 'voice': return '语音咨询';
      case 'offline': return '线下咨询';
      default: return '视频咨询';
    }
  };

  const handleConfirmBooking = async () => {
    try {
      const currentUserId = getCurrentUserId();
      console.log('Creating appointment for user:', currentUserId);
      
      const result = await createAppointment.mutateAsync({
        tutor_id: tutorId!,
        student_id: currentUserId,
        appointment_date: selectedDate,
        start_time: startTime,
        end_time: endTime,
        duration,
        price: tutor.price,
        meeting_type: meetingType as 'video' | 'voice' | 'offline',
        location: meetingType === 'offline' ? location : undefined,
        notes: notes || undefined
      });

      // 设置订单ID并显示成功状态
      setOrderId(result.id);
      setIsSubmitted(true);
      
      console.log('Appointment created successfully:', result);

      // 3秒后跳转到预约列表页面
      setTimeout(() => {
        navigate('/appointments');
      }, 3000);
    } catch (error) {
      console.error('Booking confirmation failed:', error);
    }
  };

  // 如果已提交成功，显示成功页面
  if (isSubmitted && orderId) {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* 顶部导航 */}
        <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
          <div className="max-w-md mx-auto flex items-center">
            <Link to="/appointments" className="mr-4">
              <ArrowLeft className="h-6 w-6 text-gray-600" />
            </Link>
            <h1 className="text-lg font-semibold">预约成功</h1>
          </div>
        </div>

        <div className="max-w-md mx-auto p-4 space-y-6">
          {/* 成功状态 */}
          <Card>
            <CardContent className="p-6 text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">预约创建成功！</h2>
              <p className="text-gray-600 mb-4">
                您的预约订单已生成，导师将收到通知并尽快确认您的预约。
              </p>
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <p className="text-sm text-gray-600">订单号</p>
                <p className="font-mono text-sm text-gray-800">{orderId.substring(0, 8).toUpperCase()}</p>
              </div>
            </CardContent>
          </Card>

          {/* 预约详情摘要 */}
          <Card>
            <CardHeader>
              <CardTitle>预约详情</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">导师</span>
                <span className="font-medium">{tutor.name}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">日期时间</span>
                <span className="font-medium">{selectedDate} {selectedTime}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">咨询方式</span>
                <Badge variant="outline">{getMeetingTypeText(meetingType)}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">费用</span>
                <span className="text-xl font-bold text-blue-600">¥{tutor.price}</span>
              </div>
            </CardContent>
          </Card>

          {/* 下一步说明 */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-medium mb-2">接下来会发生什么？</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">1</div>
                  <p>导师将收到您的预约通知</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">2</div>
                  <p>导师确认后您将收到通知</p>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">3</div>
                  <p>在预约时间进行咨询</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="space-y-3">
            <Button 
              onClick={() => navigate('/appointments')}
              className="w-full h-12 text-lg"
            >
              查看我的预约
            </Button>
            <p className="text-xs text-gray-500 text-center">
              页面将在3秒后自动跳转到预约列表
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-md mx-auto flex items-center">
          <Link to={`/tutor-profile?id=${tutorId}`} className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">确认预约</h1>
        </div>
      </div>

      <div className="max-w-md mx-auto p-4 space-y-6">
        {/* 导师信息 */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium">{tutor.name}</h3>
                <p className="text-sm text-gray-500">
                  {tutor.university} · {tutor.major}
                </p>
                <p className="text-sm text-gray-500">{tutor.current_job}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 预约详情 */}
        <Card>
          <CardHeader>
            <CardTitle>预约详情</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-gray-600">
                <Calendar className="h-4 w-4" />
                <span>日期</span>
              </div>
              <span className="font-medium">{selectedDate}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-gray-600">
                <Clock className="h-4 w-4" />
                <span>时间</span>
              </div>
              <span className="font-medium">{selectedTime}</span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-gray-600">
                {getMeetingTypeIcon(meetingType)}
                <span>咨询方式</span>
              </div>
              <Badge variant="outline">{getMeetingTypeText(meetingType)}</Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">咨询时长</span>
              <span className="font-medium">{duration}分钟</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">咨询费用</span>
              <span className="text-xl font-bold text-blue-600">¥{tutor.price}</span>
            </div>
          </CardContent>
        </Card>

        {/* 线下咨询地点 */}
        {meetingType === 'offline' && (
          <Card>
            <CardHeader>
              <CardTitle>咨询地点</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="请输入咨询地点"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="min-h-[80px]"
              />
            </CardContent>
          </Card>
        )}

        {/* 备注信息 */}
        <Card>
          <CardHeader>
            <CardTitle>备注信息</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="请描述您希望咨询的问题或特殊要求（选填）"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="min-h-[100px]"
            />
          </CardContent>
        </Card>

        {/* 确认按钮 */}
        <div className="space-y-3">
          <Button 
            onClick={handleConfirmBooking}
            disabled={createAppointment.isPending || (meetingType === 'offline' && !location)}
            className="w-full h-12 text-lg"
          >
            {createAppointment.isPending ? '提交中...' : '确认预约'}
          </Button>
          <p className="text-sm text-gray-500 text-center">
            确认后将生成预约订单并通知导师确认
          </p>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirmation;

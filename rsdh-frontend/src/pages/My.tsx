
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  User, 
  Calendar, 
  Heart, 
  Settings, 
  HelpCircle, 
  FileText, 
  Shield,
  ChevronRight,
  Star,
  Home,
  Bell,
  Wallet,
  Clock,
  TrendingUp
} from "lucide-react";
import { Link } from "react-router-dom";
import { useUserAppointments } from "@/hooks/useUserAppointments";

// 模拟当前用户信息 - 在实际应用中这应该来自认证系统
const CURRENT_USER = {
  id: "mock-user-id",
  name: "张同学",
  avatar: null,
  phone: "138****5678",
  registeredDays: 30,
  isVip: true,
  consultationCount: 12,
  balance: 500,
  rating: 5.0
};

const My = () => {
  const { data: appointments = [], isLoading } = useUserAppointments(CURRENT_USER.id);

  // 计算统计数据
  const completedAppointments = appointments.filter(apt => apt.status === 'completed').length;
  const totalSpent = appointments
    .filter(apt => apt.status === 'completed')
    .reduce((sum, apt) => sum + (apt.price || 0), 0);
  const upcomingAppointments = appointments.filter(apt => 
    apt.status === 'confirmed' || apt.status === 'pending'
  ).length;

  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const menuItems = [
    {
      icon: <Calendar className="h-5 w-5" />,
      title: "我的预约",
      subtitle: `${upcomingAppointments}个即将开始`,
      link: "/appointments",
      color: "text-blue-600"
    },
    {
      icon: <Heart className="h-5 w-5" />,
      title: "我的收藏",
      subtitle: "收藏的导师",
      link: "/favorites",
      color: "text-red-500"
    },
    {
      icon: <Star className="h-5 w-5" />,
      title: "我的评价",
      subtitle: "给导师的评价",
      link: "/reviews",
      color: "text-yellow-500"
    },
    {
      icon: <User className="h-5 w-5" />,
      title: "个人资料",
      subtitle: "编辑个人信息",
      link: "/profile",
      color: "text-green-600"
    }
  ];

  const settingsItems = [
    {
      icon: <Settings className="h-5 w-5" />,
      title: "设置",
      link: "/settings"
    },
    {
      icon: <HelpCircle className="h-5 w-5" />,
      title: "帮助与反馈",
      link: "/feedback"
    },
    {
      icon: <FileText className="h-5 w-5" />,
      title: "用户协议",
      link: "/user-agreement"
    },
    {
      icon: <Shield className="h-5 w-5" />,
      title: "隐私政策",
      link: "/privacy-policy"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/" className="mr-4">
                <Home className="h-6 w-6 text-gray-600" />
              </Link>
              <h1 className="text-lg font-semibold">我的</h1>
            </div>
            <Link to="/profile">
              <Button variant="ghost" size="sm" className="text-gray-600">
                编辑资料
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* 用户信息卡片 */}
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 pt-8 pb-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center space-x-6 mb-6">
            <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center text-3xl">
              {CURRENT_USER.avatar || <User className="h-10 w-10 text-white" />}
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-semibold text-white">{CURRENT_USER.name}</h2>
                {CURRENT_USER.isVip && (
                  <Badge variant="secondary" className="bg-yellow-400 text-yellow-800">
                    <Star className="h-3 w-3 mr-1" />
                    VIP会员
                  </Badge>
                )}
              </div>
              <p className="text-blue-100 text-sm mt-1">{CURRENT_USER.phone}</p>
              <div className="flex items-center space-x-4 mt-3">
                <span className="text-xs text-blue-100">注册{CURRENT_USER.registeredDays}天</span>
              </div>
            </div>
          </div>

          {/* 统计信息 */}
          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="bg-white/10 rounded-lg p-3 text-center backdrop-blur-sm">
              <div className="text-xl font-bold text-white">{CURRENT_USER.consultationCount}</div>
              <div className="text-xs text-blue-100">咨询次数</div>
            </div>
            <div className="bg-white/10 rounded-lg p-3 text-center backdrop-blur-sm">
              <div className="text-xl font-bold text-white">¥{CURRENT_USER.balance}</div>
              <div className="text-xs text-blue-100">账户余额</div>
            </div>
            <div className="bg-white/10 rounded-lg p-3 text-center backdrop-blur-sm">
              <div className="text-xl font-bold text-white">{CURRENT_USER.rating.toFixed(1)}</div>
              <div className="text-xs text-blue-100">满意度</div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6 space-y-6">
        {/* 我的服务 */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-base font-medium">我的服务</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
              {menuItems.map((item, index) => (
                <Link
                  key={index}
                  to={item.link}
                  className="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className={`p-3 rounded-full ${item.color} bg-opacity-10 mb-2`}>
                    {React.cloneElement(item.icon, { className: `h-6 w-6 ${item.color}` })}
                  </div>
                  <span className="text-sm font-medium text-gray-900">{item.title}</span>
                  <span className="text-xs text-gray-500 text-center">{item.subtitle}</span>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 设置 */}
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-base font-medium">设置</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-gray-100">
              {settingsItems.map((item, index) => (
                <Link
                  key={index}
                  to={item.link}
                  className="flex items-center justify-between p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className="text-gray-500">{item.icon}</div>
                    <span className="text-sm">{item.title}</span>
                  </div>
                  <ChevronRight className="h-4 w-4 text-gray-400" />
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 数据统计 */}
        <Card className="mx-4 mt-4 border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Clock className="h-5 w-5 text-blue-500 mr-1" />
                </div>
                <p className="text-2xl font-bold text-blue-600">
                  {isLoading ? '-' : completedAppointments}
                </p>
                <p className="text-xs text-gray-500">已完成咨询</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="h-5 w-5 text-green-500 mr-1" />
                </div>
                <p className="text-2xl font-bold text-green-600">
                  {isLoading ? '-' : `¥${totalSpent}`}
                </p>
                <p className="text-xs text-gray-500">累计消费</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Calendar className="h-5 w-5 text-orange-500 mr-1" />
                </div>
                <p className="text-2xl font-bold text-orange-600">
                  {isLoading ? '-' : upcomingAppointments}
                </p>
                <p className="text-xs text-gray-500">即将开始</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 主要功能菜单 */}
        <Card className="mx-4 mt-4 border-0 shadow-sm">
          <CardContent className="p-0">
            {menuItems.map((item, index) => (
              <Link key={index} to={item.link}>
                <div className="flex items-center justify-between p-4 border-b last:border-b-0 border-gray-100 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={item.color}>
                      {item.icon}
                    </div>
                    <div>
                      <p className="font-medium">{item.title}</p>
                      <p className="text-sm text-gray-500">{item.subtitle}</p>
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>
              </Link>
            ))}
          </CardContent>
        </Card>

        {/* 设置菜单 */}
        <Card className="mx-4 mt-4 border-0 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-base">服务与支持</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {settingsItems.map((item, index) => (
              <Link key={index} to={item.link}>
                <div className="flex items-center justify-between p-4 border-b last:border-b-0 border-gray-100 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className="text-gray-600">
                      {item.icon}
                    </div>
                    <p className="font-medium">{item.title}</p>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>
              </Link>
            ))}
          </CardContent>
        </Card>

        {/* 退出登录 */}
        <div className="mx-4 mt-4 mb-8">
          <Button variant="outline" className="w-full" size="lg">
            退出登录
          </Button>
        </div>
      </div>

      {/* 移动端底部导航 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-3 md:hidden z-50">
        <Button 
          variant="outline" 
          className="w-full" 
          onClick={() => console.log('退出登录')}
        >
          退出登录
        </Button>
      </div>
      <div className="h-16 md:h-0"></div> {/* 为移动端底部导航留出空间 */}
    </div>
  );
};

export default My;

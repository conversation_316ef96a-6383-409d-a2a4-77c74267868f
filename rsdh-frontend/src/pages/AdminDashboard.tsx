
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertTriangle, MapPin, BarChart3, Users, Ban, Trash2 } from "lucide-react";
import { 
  useAdminStats, 
  useWarningTutors, 
  useProvinceStats, 
  useIndustryStats, 
  useLowRatingReviews 
} from "@/hooks/useAdminStats";

const AdminDashboard = () => {
  const { data: stats, isLoading: statsLoading } = useAdminStats();
  const { data: warningTutors = [], isLoading: warningLoading } = useWarningTutors();
  const { data: topProvinces = [], isLoading: provinceLoading } = useProvinceStats();
  const { data: industryData = [], isLoading: industryLoading } = useIndustryStats();
  const { data: recentReviews = [], isLoading: reviewsLoading } = useLowRatingReviews();

  const handleBanTutor = (tutorId: string) => {
    console.log('封禁导师:', tutorId);
    // TODO: 实现封禁功能
  };

  const handleDeleteReview = (reviewId: string) => {
    console.log('删除评价:', reviewId);
    // TODO: 实现删除评价功能
  };

  if (statsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-xl font-bold text-gray-800">管理员控制台</h1>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4 space-y-6">
        {/* 核心指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">总导师数</p>
                  <p className="text-2xl font-bold">{stats?.tutorCount || 0}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">本月咨询</p>
                  <p className="text-2xl font-bold">{stats?.appointmentCount || 0}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">待审核导师</p>
                  <p className="text-2xl font-bold">{stats?.pendingTutorCount || 0}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">平均评分</p>
                  <p className="text-2xl font-bold">{stats?.averageRating || 0}</p>
                </div>
                <div className="text-yellow-500 text-2xl">⭐</div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 高危预警 */}
          <Card>
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <CardTitle className="text-base font-semibold flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                高危预警
              </CardTitle>
            </CardHeader>
            <CardContent>
              {warningLoading ? (
                <div className="animate-pulse space-y-3">
                  <div className="h-16 bg-gray-200 rounded"></div>
                  <div className="h-16 bg-gray-200 rounded"></div>
                </div>
              ) : warningTutors.length > 0 ? (
                <div className="space-y-3">
                  {warningTutors.map((tutor) => (
                    <div key={tutor.id} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div>
                        <p className="font-medium">{tutor.name}</p>
                        <p className="text-sm text-gray-600">
                          评分: {tutor.rating} | 投诉: {tutor.complaints}次
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="destructive">{tutor.status}</Badge>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleBanTutor(tutor.id)}
                        >
                          <Ban className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">暂无需要关注的导师</p>
              )}
            </CardContent>
          </Card>

          {/* 地域热力图 */}
          <Card>
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <CardTitle className="text-base font-semibold flex items-center">
                <MapPin className="h-5 w-5 text-blue-500 mr-2" />
                咨询量Top5省份
              </CardTitle>
            </CardHeader>
            <CardContent>
              {provinceLoading ? (
                <div className="animate-pulse space-y-3">
                  {[1, 2, 3, 4, 5].map(i => (
                    <div key={i} className="h-8 bg-gray-200 rounded"></div>
                  ))}
                </div>
              ) : topProvinces.length > 0 ? (
                <div className="space-y-3">
                  {topProvinces.map((item, index) => {
                    const maxConsultations = topProvinces[0]?.consultations || 1;
                    return (
                      <div key={item.province} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="w-6 h-6 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center">
                            {index + 1}
                          </span>
                          <span className="font-medium">{item.province}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 h-2 bg-gray-200 rounded-full">
                            <div 
                              className="h-2 bg-blue-500 rounded-full"
                              style={{ width: `${(item.consultations / maxConsultations) * 100}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-600 w-8">
                            {item.consultations}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">暂无数据</p>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 行业分布 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base font-semibold flex items-center">
              <BarChart3 className="h-5 w-5 text-purple-500 mr-2" />
              行业分布
            </CardTitle>
          </CardHeader>
          <CardContent>
            {industryLoading ? (
              <div className="animate-pulse grid grid-cols-1 md:grid-cols-5 gap-4">
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 bg-gray-200 rounded-full"></div>
                    <div className="h-4 bg-gray-200 rounded mb-1"></div>
                    <div className="h-3 bg-gray-200 rounded"></div>
                  </div>
                ))}
              </div>
            ) : industryData.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {industryData.map((item) => (
                  <div key={item.industry} className="text-center">
                    <div className="w-16 h-16 mx-auto mb-2 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-bold">{item.count}</span>
                    </div>
                    <p className="text-sm font-medium">{item.industry}</p>
                    <p className="text-xs text-gray-500">{item.percentage}%</p>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">暂无数据</p>
            )}
          </CardContent>
        </Card>

        {/* 差评管理 */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base font-semibold">差评管理</CardTitle>
          </CardHeader>
          <CardContent>
            {reviewsLoading ? (
              <div className="animate-pulse">
                <div className="h-40 bg-gray-200 rounded"></div>
              </div>
            ) : recentReviews.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>学生</TableHead>
                    <TableHead>导师</TableHead>
                    <TableHead>评分</TableHead>
                    <TableHead>评价内容</TableHead>
                    <TableHead>日期</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {recentReviews.map((review) => (
                    <TableRow key={review.id}>
                      <TableCell>{review.student}</TableCell>
                      <TableCell>{review.tutor}</TableCell>
                      <TableCell>
                        <Badge variant="destructive">{review.rating}</Badge>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">
                        {review.comment}
                      </TableCell>
                      <TableCell>{review.date}</TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteReview(review.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <p className="text-gray-500 text-center py-4">暂无差评数据</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Calendar, Clock, MapPin, Video, MessageSquare } from "lucide-react";
import { Link } from "react-router-dom";
import { useUserAppointments } from "@/hooks/useUserAppointments";

// 获取当前用户ID（与 BookingConfirmation 保持一致）
const getCurrentUserId = () => {
  let userId = localStorage.getItem('current_user_id');
  if (!userId) {
    const generateUUID = () => {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    };
    userId = generateUUID();
    localStorage.setItem('current_user_id', userId);
  }
  return userId;
};

const Appointments = () => {
  const [activeTab, setActiveTab] = useState("upcoming");
  const currentUserId = getCurrentUserId();
  const { data: appointments = [], isLoading, error } = useUserAppointments(currentUserId);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed': return '已确认';
      case 'pending': return '待确认';
      case 'completed': return '已完成';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  const getMeetingTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4" />;
      case 'voice': return <MessageSquare className="h-4 w-4" />;
      case 'offline': return <MapPin className="h-4 w-4" />;
      default: return <Video className="h-4 w-4" />;
    }
  };

  const getMeetingTypeText = (type: string) => {
    switch (type) {
      case 'video': return '视频咨询';
      case 'voice': return '语音咨询';
      case 'offline': return '线下咨询';
      default: return '视频咨询';
    }
  };

  const filterAppointments = (status: string) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    return appointments.filter(appointment => {
      const appointmentDate = new Date(appointment.appointment_date);
      
      switch (status) {
        case 'upcoming':
          return appointmentDate >= today && (appointment.status === 'confirmed' || appointment.status === 'pending');
        case 'completed':
          return appointment.status === 'completed';
        case 'cancelled':
          return appointment.status === 'cancelled';
        default:
          return true;
      }
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">加载失败，请重试</p>
          <Button onClick={() => window.location.reload()}>重新加载</Button>
        </div>
      </div>
    );
  }

  const upcomingAppointments = filterAppointments('upcoming');
  const completedAppointments = filterAppointments('completed');
  const cancelledAppointments = filterAppointments('cancelled');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-md mx-auto flex items-center">
          <Link to="/my" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">我的预约</h1>
        </div>
      </div>

      <div className="max-w-md mx-auto p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upcoming">即将开始</TabsTrigger>
            <TabsTrigger value="completed">已完成</TabsTrigger>
            <TabsTrigger value="cancelled">已取消</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="space-y-4 mt-4">
            {upcomingAppointments.length > 0 ? (
              upcomingAppointments.map((appointment) => (
                <Card key={appointment.id} className="border-0 shadow-sm">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-medium">
                            {appointment.tutor?.name?.charAt(0) || 'T'}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium">{appointment.tutor?.name || '导师'}</h3>
                          <p className="text-sm text-gray-500">
                            {appointment.tutor?.university} · {appointment.tutor?.major}
                          </p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(appointment.status || 'pending')}>
                        {getStatusText(appointment.status || 'pending')}
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{appointment.appointment_date}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{appointment.start_time} - {appointment.end_time}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        {getMeetingTypeIcon(appointment.meeting_type || 'video')}
                        <span>{getMeetingTypeText(appointment.meeting_type || 'video')}</span>
                        {appointment.location && (
                          <span>· {appointment.location}</span>
                        )}
                      </div>
                    </div>

                    {appointment.notes && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                        <p className="text-sm text-gray-600">{appointment.notes}</p>
                      </div>
                    )}

                    <div className="flex justify-between items-center mt-4">
                      <span className="text-lg font-semibold text-blue-600">¥{appointment.price}</span>
                      <div className="space-x-2">
                        {appointment.status === 'pending' && (
                          <Button size="sm" variant="outline">取消预约</Button>
                        )}
                        {appointment.status === 'confirmed' && (
                          <Button size="sm">加入咨询</Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">暂无即将开始的预约</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="completed" className="space-y-4 mt-4">
            {completedAppointments.length > 0 ? (
              completedAppointments.map((appointment) => (
                <Card key={appointment.id} className="border-0 shadow-sm">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 font-medium">
                            {appointment.tutor?.name?.charAt(0) || 'T'}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium">{appointment.tutor?.name || '导师'}</h3>
                          <p className="text-sm text-gray-500">
                            {appointment.tutor?.university} · {appointment.tutor?.major}
                          </p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(appointment.status || 'completed')}>
                        {getStatusText(appointment.status || 'completed')}
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{appointment.appointment_date}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{appointment.start_time} - {appointment.end_time}</span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <span className="text-lg font-semibold text-green-600">¥{appointment.price}</span>
                      <Button size="sm" variant="outline">写评价</Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">暂无已完成的预约</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="cancelled" className="space-y-4 mt-4">
            {cancelledAppointments.length > 0 ? (
              cancelledAppointments.map((appointment) => (
                <Card key={appointment.id} className="border-0 shadow-sm opacity-75">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                          <span className="text-gray-600 font-medium">
                            {appointment.tutor?.name?.charAt(0) || 'T'}
                          </span>
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-700">{appointment.tutor?.name || '导师'}</h3>
                          <p className="text-sm text-gray-500">
                            {appointment.tutor?.university} · {appointment.tutor?.major}
                          </p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(appointment.status || 'cancelled')}>
                        {getStatusText(appointment.status || 'cancelled')}
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{appointment.appointment_date}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>{appointment.start_time} - {appointment.end_time}</span>
                      </div>
                    </div>

                    <div className="flex justify-between items-center mt-4">
                      <span className="text-lg font-semibold text-gray-500">¥{appointment.price}</span>
                      <Button size="sm" variant="outline" disabled>已取消</Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">暂无已取消的预约</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Appointments;


import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Search, Filter, Star, MapPin, GraduationCap, Briefcase } from "lucide-react";
import { Link } from "react-router-dom";
import { useTutors } from "@/hooks/useTutors";

const Tutors = () => {
  const [searchText, setSearchText] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('all');
  const [selectedUniversity, setSelectedUniversity] = useState('all');
  const [selectedYear, setSelectedYear] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  const { data: tutors = [], isLoading } = useTutors({
    searchText: searchText || undefined,
    province: selectedProvince,
    university: selectedUniversity,
    year: selectedYear,
  });

  const provinces = ['北京', '上海', '广东', '江苏', '浙江', '山东', '河南', '湖北', '湖南', '四川'];
  const universities = ['清华大学', '北京大学', '复旦大学', '上海交通大学', '浙江大学', '中国科学技术大学'];
  const years = Array.from({ length: 10 }, (_, i) => (new Date().getFullYear() - i).toString());

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto flex items-center">
          <Link to="/" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold flex-1">导师库</h1>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-1" />
            筛选
          </Button>
        </div>
      </div>

      <div className="max-w-6xl mx-auto p-4">
        {/* 搜索框 */}
        <div className="mb-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="搜索导师、专业或学校"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              className="pl-10 h-12"
            />
          </div>
        </div>

        {/* 筛选器 */}
        {showFilters && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">高考省份</label>
                  <Select value={selectedProvince} onValueChange={setSelectedProvince}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择省份" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部省份</SelectItem>
                      {provinces.map(province => (
                        <SelectItem key={province} value={province}>{province}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">毕业院校</label>
                  <Select value={selectedUniversity} onValueChange={setSelectedUniversity}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择学校" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部学校</SelectItem>
                      {universities.map(uni => (
                        <SelectItem key={uni} value={uni}>{uni}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">毕业年份</label>
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger>
                      <SelectValue placeholder="选择年份" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部年份</SelectItem>
                      {years.map(year => (
                        <SelectItem key={year} value={year}>{year}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 导师列表 */}
        {isLoading ? (
          <div className="text-center py-12 text-gray-500">加载中...</div>
        ) : (
          <div className="space-y-4">
            {tutors.map(tutor => (
              <Card key={tutor.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-4">
                    {/* 头像 */}
                    <div className="text-4xl">👨‍💼</div>
                    
                    {/* 主要信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{tutor.name}</h3>
                        <div className="text-right">
                          <div className="text-xl font-bold text-blue-600">¥{tutor.price}</div>
                          <div className="text-sm text-gray-500">/次</div>
                        </div>
                      </div>

                      {/* 教育背景 */}
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <GraduationCap className="h-4 w-4 mr-1" />
                        <span>{tutor.university} · {tutor.major} · {tutor.graduation_year}届</span>
                        {tutor.gaokao_score && (
                          <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                            高考{tutor.gaokao_score}分({tutor.gaokao_province})
                          </span>
                        )}
                      </div>

                      {/* 职业信息 */}
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <Briefcase className="h-4 w-4 mr-1" />
                        <span>{tutor.company} · {tutor.current_job}</span>
                      </div>

                      {/* 地理位置 */}
                      <div className="flex items-center text-sm text-gray-600 mb-3">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span>{tutor.province}</span>
                      </div>

                      {/* 评分和标签 */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center">
                            <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                            <span className="text-sm font-medium">{tutor.rating}</span>
                            <span className="text-sm text-gray-500 ml-1">({tutor.review_count}评价)</span>
                          </div>
                        </div>
                        
                        <Link to={`/tutor-profile?id=${tutor.id}`}>
                          <Button size="sm">查看详情</Button>
                        </Link>
                      </div>

                      {/* 专长标签 */}
                      {tutor.tags && tutor.tags.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-3">
                          {tutor.tags.map((tag, index) => (
                            <span 
                              key={index} 
                              className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* 无结果提示 */}
        {!isLoading && tutors.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">未找到匹配的导师</h3>
            <p className="text-gray-500">尝试调整筛选条件或搜索关键词</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Tutors;

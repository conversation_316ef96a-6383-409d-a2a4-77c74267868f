
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLef<PERSON>, Star } from "lucide-react";
import { <PERSON> } from "react-router-dom";

interface Rating {
  professional: number;
  planning: number;
  communication: number;
}

const Feedback = () => {
  const [ratings, setRatings] = useState<Rating>({
    professional: 0,
    planning: 0,
    communication: 0
  });
  const [comment, setComment] = useState('');
  const [submitted, setSubmitted] = useState(false);

  const ratingLabels = {
    professional: '专业度',
    planning: '规划前瞻性', 
    communication: '沟通亲和力'
  };

  const handleRating = (category: keyof Rating, score: number) => {
    setRatings(prev => ({ ...prev, [category]: score }));
  };

  const renderStars = (category: keyof Rating) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            onClick={() => handleRating(category, star)}
            className="focus:outline-none"
          >
            <Star
              className={`h-8 w-8 ${
                star <= ratings[category]
                  ? 'text-yellow-400 fill-current'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    );
  };

  const handleSubmit = () => {
    console.log('提交评价:', { ratings, comment });
    setSubmitted(true);
  };

  const averageRating = Object.values(ratings).reduce((a, b) => a + b, 0) / 3;
  const isValidRating = Object.values(ratings).every(rating => rating > 0);

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto p-4 text-center">
          <div className="text-6xl mb-6">🎉</div>
          <h2 className="text-xl font-bold mb-4">感谢您的评价！</h2>
          <p className="text-gray-600 mb-6">
            您的反馈将帮助我们为您提供更好的服务
          </p>
          <div className="space-y-3">
            <Link to="/">
              <Button className="w-full">返回首页</Button>
            </Link>
            <Button variant="outline" className="w-full">
              邀请好友得咨询券
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-md mx-auto flex items-center">
          <Link to="/appointments" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">服务评价</h1>
        </div>
      </div>

      <div className="max-w-md mx-auto p-4">
        {/* 导师信息 */}
        <Card className="border-0 shadow-sm mb-4">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="text-3xl">👨‍💼</div>
              <div>
                <h3 className="font-semibold">张导师</h3>
                <div className="flex flex-wrap gap-1 mt-1">
                  <span className="bg-blue-50 text-blue-600 text-xs px-2 py-1 rounded">
                    清华·计算机
                  </span>
                  <span className="bg-blue-50 text-blue-600 text-xs px-2 py-1 rounded">
                    腾讯5年
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 评分区域 */}
        <Card className="border-0 shadow-sm mb-4">
          <CardHeader>
            <CardTitle className="text-base">请为本次服务评分</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {Object.entries(ratingLabels).map(([key, label]) => (
              <div key={key} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{label}</span>
                  <span className="text-sm text-gray-500">
                    {ratings[key as keyof Rating]}/5
                  </span>
                </div>
                {renderStars(key as keyof Rating)}
              </div>
            ))}

            {isValidRating && (
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <p className="text-blue-600 font-semibold">
                  综合评分: {averageRating.toFixed(1)}/5.0
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 文字评价 */}
        <Card className="border-0 shadow-sm mb-6">
          <CardHeader>
            <CardTitle className="text-base">详细评价（可选）</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="请分享您的具体感受，帮助其他同学了解导师的指导效果..."
              rows={4}
              maxLength={300}
              className="resize-none"
            />
            <p className="text-xs text-gray-500 mt-2 text-right">
              {comment.length}/300
            </p>
          </CardContent>
        </Card>

        {/* 评价标签 */}
        <Card className="border-0 shadow-sm mb-6">
          <CardHeader>
            <CardTitle className="text-base">快速标签</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {[
                '专业权威', '耐心细致', '思路清晰', '经验丰富',
                '视野开阔', '态度亲和', '回复及时', '建议实用'
              ].map((tag) => (
                <button
                  key={tag}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-colors"
                >
                  {tag}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 提交按钮 */}
        <Button
          onClick={handleSubmit}
          disabled={!isValidRating}
          className="w-full h-12"
        >
          提交评价
        </Button>

        <div className="text-center mt-4">
          <p className="text-xs text-gray-500">
            您的评价将帮助其他学生选择合适的导师
          </p>
        </div>
      </div>
    </div>
  );
};

export default Feedback;


import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowLeft, Star, MapPin, GraduationCap, Briefcase, Calendar, Clock, MessageCircle, Video, MessageSquare } from "lucide-react";
import { Link, useSearchParams, useNavigate } from "react-router-dom";
import { useTutorDetails } from "@/hooks/useTutorDetails";

const TutorProfile = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const tutorId = searchParams.get('id');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>('');
  const [meetingType, setMeetingType] = useState<string>('video');

  const { data, isLoading, error } = useTutorDetails(tutorId || '');

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 text-6xl mb-4">👨‍💼</div>
          <p className="text-gray-500">加载导师信息中...</p>
        </div>
      </div>
    );
  }

  if (error || !data?.tutor) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 text-6xl mb-4">😕</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">找不到导师信息</h3>
          <p className="text-gray-500 mb-4">该导师可能不存在或已停用</p>
          <Link to="/tutors">
            <Button>返回导师列表</Button>
          </Link>
        </div>
      </div>
    );
  }

  const { tutor, education, career, availability, reviews } = data;

  const handleBooking = () => {
    if (!selectedTimeSlot) return;
    
    const [date, time] = selectedTimeSlot.split(' ');
    const params = new URLSearchParams({
      tutorId: tutorId!,
      date,
      time,
      meetingType
    });
    
    navigate(`/booking-confirmation?${params.toString()}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto flex items-center">
          <Link to="/tutors" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">导师详情</h1>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4 space-y-6">
        {/* 导师基本信息 */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-start space-x-4">
              <div className="text-6xl">👨‍💼</div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-4">
                  <h1 className="text-2xl font-bold text-gray-900">{tutor.name}</h1>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-blue-600">¥{tutor.price}</div>
                    <div className="text-sm text-gray-500">/次咨询</div>
                  </div>
                </div>

                {/* 基本标签 */}
                <div className="space-y-2 mb-4">
                  <div className="flex items-center text-gray-600">
                    <GraduationCap className="h-4 w-4 mr-2" />
                    <span>{tutor.university} · {tutor.major} · {tutor.graduation_year}届</span>
                    {tutor.gaokao_score && (
                      <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                        高考{tutor.gaokao_score}分({tutor.gaokao_province})
                      </span>
                    )}
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Briefcase className="h-4 w-4 mr-2" />
                    <span>{tutor.company} · {tutor.current_job}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span>{tutor.province}</span>
                  </div>
                </div>

                {/* 评分和统计 */}
                <div className="flex items-center space-x-6 mb-4">
                  <div className="flex items-center">
                    <Star className="h-5 w-5 text-yellow-400 fill-current mr-1" />
                    <span className="font-medium">{tutor.rating}</span>
                    <span className="text-gray-500 ml-1">({tutor.review_count}评价)</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    已咨询 {tutor.consultation_count} 人次
                  </div>
                </div>

                {/* 专长标签 */}
                {tutor.tags && tutor.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {tutor.tags.map((tag, index) => (
                      <span 
                        key={index} 
                        className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 个人介绍 */}
        {tutor.bio && (
          <Card>
            <CardHeader>
              <CardTitle>个人介绍</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 leading-relaxed">{tutor.bio}</p>
            </CardContent>
          </Card>
        )}

        {/* 教育经历 */}
        {education.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>教育经历</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {education.map((edu) => (
                  <div key={edu.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="text-3xl">🎓</div>
                    <div>
                      <h3 className="font-semibold">{edu.school}</h3>
                      <p className="text-gray-600">{edu.degree} · {edu.major}</p>
                      <p className="text-sm text-gray-500">{edu.start_year}-{edu.end_year}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 职业经历 */}
        {career.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>职业经历</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {career.map((job) => (
                  <div key={job.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="text-3xl">💼</div>
                    <div>
                      <h3 className="font-semibold">{job.company}</h3>
                      <p className="text-gray-600">{job.position}</p>
                      <p className="text-sm text-gray-500">
                        {job.start_date} - {job.is_current ? '至今' : job.end_date}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 辅导专长 */}
        {tutor.specialties && tutor.specialties.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>辅导专长</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {tutor.specialties.map((specialty, index) => (
                  <div key={index} className="flex items-center p-3 bg-green-50 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-green-700">{specialty}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 咨询方式选择 */}
        <Card>
          <CardHeader>
            <CardTitle>选择咨询方式</CardTitle>
          </CardHeader>
          <CardContent>
            <Select value={meetingType} onValueChange={setMeetingType}>
              <SelectTrigger>
                <SelectValue placeholder="选择咨询方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="video">
                  <div className="flex items-center space-x-2">
                    <Video className="h-4 w-4" />
                    <span>视频咨询</span>
                  </div>
                </SelectItem>
                <SelectItem value="voice">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="h-4 w-4" />
                    <span>语音咨询</span>
                  </div>
                </SelectItem>
                <SelectItem value="offline">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4" />
                    <span>线下咨询</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* 预约时间 */}
        {availability.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                可预约时间
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {availability.map((slot) => (
                  <button
                    key={slot.id}
                    onClick={() => setSelectedTimeSlot(`${slot.date} ${slot.start_time}-${slot.end_time}`)}
                    className={`p-3 rounded-lg border text-sm ${
                      selectedTimeSlot === `${slot.date} ${slot.start_time}-${slot.end_time}`
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white hover:bg-blue-50 border-gray-200'
                    }`}
                  >
                    <div className="font-medium">{slot.date}</div>
                    <div className="flex items-center justify-center mt-1">
                      <Clock className="h-3 w-3 mr-1" />
                      {slot.start_time}-{slot.end_time}
                    </div>
                  </button>
                ))}
              </div>
              
              {selectedTimeSlot && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-700 font-medium">已选择时间：{selectedTimeSlot}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* 学员评价 */}
        {reviews.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageCircle className="h-5 w-5 mr-2" />
                学员评价
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reviews.map((review) => (
                  <div key={review.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">学员</span>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                    {review.content && (
                      <p className="text-gray-700 mb-2">{review.content}</p>
                    )}
                    <p className="text-sm text-gray-500">
                      {review.created_at ? new Date(review.created_at).toLocaleDateString() : ''}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* 预约按钮 */}
        <div className="sticky bottom-4">
          <Button 
            onClick={handleBooking}
            disabled={!selectedTimeSlot}
            className="w-full h-12 text-lg"
          >
            {selectedTimeSlot ? '立即预约咨询' : '请先选择时间'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TutorProfile;

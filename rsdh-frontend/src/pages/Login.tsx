
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Smartphone, MessageSquare } from "lucide-react";
import { Link } from "react-router-dom";

const Login = () => {
  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [countdown, setCountdown] = useState(0);

  const sendCode = () => {
    if (phone.length === 11) {
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
  };

  const handleWechatLogin = () => {
    // 模拟微信登录
    console.log('微信登录');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-md mx-auto flex items-center">
          <Link to="/" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">登录</h1>
        </div>
      </div>

      <div className="max-w-md mx-auto p-4">
        {/* Logo区域 */}
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🧭</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">人生领航员</h2>
          <p className="text-gray-600 text-sm">我们不辅导志愿，我们辅导人生</p>
        </div>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-center text-lg">欢迎登录</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="phone" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="phone" className="flex items-center">
                  <Smartphone className="h-4 w-4 mr-2" />
                  手机登录
                </TabsTrigger>
                <TabsTrigger value="wechat" className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  微信登录
                </TabsTrigger>
              </TabsList>

              <TabsContent value="phone" className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    手机号码
                  </label>
                  <Input
                    type="tel"
                    placeholder="请输入11位手机号"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    className="h-12"
                    maxLength={11}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    验证码
                  </label>
                  <div className="flex space-x-2">
                    <Input
                      type="text"
                      placeholder="请输入验证码"
                      value={code}
                      onChange={(e) => setCode(e.target.value)}
                      className="h-12 flex-1"
                      maxLength={6}
                    />
                    <Button
                      variant="outline"
                      onClick={sendCode}
                      disabled={phone.length !== 11 || countdown > 0}
                      className="h-12 px-4 whitespace-nowrap"
                    >
                      {countdown > 0 ? `${countdown}s` : '获取验证码'}
                    </Button>
                  </div>
                </div>

                <Button 
                  className="w-full h-12 mt-6"
                  disabled={phone.length !== 11 || code.length !== 6}
                >
                  立即登录
                </Button>
              </TabsContent>

              <TabsContent value="wechat" className="space-y-4">
                <div className="text-center py-8">
                  <div className="text-6xl mb-4">💬</div>
                  <p className="text-gray-600 mb-6">使用微信账号快速登录</p>
                  <Button 
                    onClick={handleWechatLogin}
                    className="w-full h-12 bg-green-500 hover:bg-green-600"
                  >
                    微信一键登录
                  </Button>
                </div>
              </TabsContent>
            </Tabs>

            {/* 协议说明 */}
            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500">
                登录即表示同意
                <Link to="/user-agreement" className="text-blue-500 mx-1">《用户协议》</Link>
                和
                <Link to="/privacy-policy" className="text-blue-500 mx-1">《隐私政策》</Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 其他登录方式 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600 mb-4">没有账号？</p>
          <div className="space-y-2">
            <Link to="/tutor-register">
              <Button variant="outline" className="w-full">
                成为导师
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;

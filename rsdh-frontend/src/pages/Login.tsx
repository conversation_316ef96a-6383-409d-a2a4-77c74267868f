
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Smartphone, MessageSquare, Mail, Lock } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

const Login = () => {
  const navigate = useNavigate();
  const { login, sendCode, register, isLoading } = useAuth();

  // Email/Password login state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Registration state
  const [regEmail, setRegEmail] = useState('');
  const [regPassword, setRegPassword] = useState('');
  const [regName, setRegName] = useState('');
  const [regCode, setRegCode] = useState('');
  const [countdown, setCountdown] = useState(0);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login({ email, password });
      navigate('/');
    } catch (error) {
      // Error is handled in the useAuth hook
    }
  };

  const handleSendCode = async () => {
    if (!regEmail) {
      toast.error('请输入邮箱地址');
      return;
    }

    try {
      await sendCode({ email: regEmail, type: 'registration' });
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      // Error is handled in the useAuth hook
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await register({
        email: regEmail,
        code: regCode,
        password: regPassword,
        name: regName,
      });
      // Switch to login tab after successful registration
      setEmail(regEmail);
    } catch (error) {
      // Error is handled in the useAuth hook
    }
  };

  const handleWechatLogin = () => {
    // 模拟微信登录
    console.log('微信登录');
    toast.info('微信登录功能暂未开放');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-md mx-auto flex items-center">
          <Link to="/" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">登录</h1>
        </div>
      </div>

      <div className="max-w-md mx-auto p-4">
        {/* Logo区域 */}
        <div className="text-center py-8">
          <div className="text-4xl mb-4">🧭</div>
          <h2 className="text-xl font-bold text-gray-800 mb-2">人生领航员</h2>
          <p className="text-gray-600 text-sm">我们不辅导志愿，我们辅导人生</p>
        </div>

        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-center text-lg">欢迎登录</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="login" className="w-full">
              <TabsList className="grid w-full grid-cols-3 mb-6">
                <TabsTrigger value="login" className="flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  登录
                </TabsTrigger>
                <TabsTrigger value="register" className="flex items-center">
                  <Lock className="h-4 w-4 mr-2" />
                  注册
                </TabsTrigger>
                <TabsTrigger value="wechat" className="flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  微信
                </TabsTrigger>
              </TabsList>

              <TabsContent value="login" className="space-y-4">
                <form onSubmit={handleLogin}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱地址
                    </label>
                    <Input
                      type="email"
                      placeholder="请输入邮箱地址"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="h-12"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      密码
                    </label>
                    <Input
                      type="password"
                      placeholder="请输入密码"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="h-12"
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 mt-6"
                    disabled={!email || !password || isLoading}
                  >
                    {isLoading ? '登录中...' : '立即登录'}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="register" className="space-y-4">
                <form onSubmit={handleRegister}>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱地址
                    </label>
                    <Input
                      type="email"
                      placeholder="请输入邮箱地址"
                      value={regEmail}
                      onChange={(e) => setRegEmail(e.target.value)}
                      className="h-12"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      姓名
                    </label>
                    <Input
                      type="text"
                      placeholder="请输入姓名"
                      value={regName}
                      onChange={(e) => setRegName(e.target.value)}
                      className="h-12"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      密码
                    </label>
                    <Input
                      type="password"
                      placeholder="请输入密码（至少8位）"
                      value={regPassword}
                      onChange={(e) => setRegPassword(e.target.value)}
                      className="h-12"
                      minLength={8}
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      验证码
                    </label>
                    <div className="flex space-x-2">
                      <Input
                        type="text"
                        placeholder="请输入验证码"
                        value={regCode}
                        onChange={(e) => setRegCode(e.target.value)}
                        className="h-12 flex-1"
                        maxLength={6}
                        required
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleSendCode}
                        disabled={!regEmail || countdown > 0}
                        className="h-12 px-4 whitespace-nowrap"
                      >
                        {countdown > 0 ? `${countdown}s` : '获取验证码'}
                      </Button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full h-12 mt-6"
                    disabled={!regEmail || !regName || !regPassword || !regCode || isLoading}
                  >
                    {isLoading ? '注册中...' : '立即注册'}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="wechat" className="space-y-4">
                <div className="text-center py-8">
                  <div className="text-6xl mb-4">💬</div>
                  <p className="text-gray-600 mb-6">使用微信账号快速登录</p>
                  <Button
                    onClick={handleWechatLogin}
                    className="w-full h-12 bg-green-500 hover:bg-green-600"
                  >
                    微信一键登录
                  </Button>
                </div>
              </TabsContent>
            </Tabs>

            {/* 协议说明 */}
            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500">
                登录即表示同意
                <Link to="/user-agreement" className="text-blue-500 mx-1">《用户协议》</Link>
                和
                <Link to="/privacy-policy" className="text-blue-500 mx-1">《隐私政策》</Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 其他登录方式 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600 mb-4">没有账号？</p>
          <div className="space-y-2">
            <Link to="/tutor-register">
              <Button variant="outline" className="w-full">
                成为导师
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;

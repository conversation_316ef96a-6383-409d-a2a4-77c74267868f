
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Star, Users, BookOpen, ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";
import ResponsiveNavbar from "@/components/ResponsiveNavbar";
import { useTutors } from "@/hooks/useTutors";

const Index = () => {
  const { data: tutors = [], isLoading } = useTutors();
  
  const features = [
    { icon: Users, title: "行业真实经验", desc: "一线从业者分享" },
    { icon: BookOpen, title: "人生规划视角", desc: "不只是志愿填报" },
    { icon: Star, title: "长期成长陪伴", desc: "职业发展指导" }
  ];

  // 取前3个导师作为推荐
  const recommendTutors = tutors.slice(0, 3);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 响应式导航栏 */}
      <ResponsiveNavbar />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20 md:pb-10">
        {/* Hero 区域 - 桌面端两栏布局 */}
        <div className="md:flex md:items-center md:justify-between md:py-16">
          <div className="md:w-1/2 md:pr-10">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              高考志愿，<br className="hidden md:block" />决定人生方向
            </h1>
            <p className="text-lg text-gray-600 mb-8 max-w-lg">
              清北学长1v1指导，助你规划理想未来。专业的高考志愿填报服务，为你的人生导航。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mb-8 md:mb-0">
              <div className="relative flex-1">
                <Input
                  type="text"
                  placeholder="搜索导师或专业..."
                  className="pl-10 h-12 text-base"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              </div>
              <Button className="h-12 text-base px-6">
                立即咨询
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
          <div className="hidden md:block md:w-1/2">
            <div className="bg-blue-50 rounded-2xl p-8 shadow-lg">
              <div className="relative aspect-video bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                <div className="text-center p-6">
                  <div className="text-4xl mb-4">🎓</div>
                  <h3 className="text-xl font-semibold text-gray-800">专业导师团队</h3>
                  <p className="text-gray-600 mt-2">来自清北等顶尖高校的学长学姐</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 特色功能 */}
        <div className="py-12 md:py-16">
          <h2 className="text-2xl md:text-3xl font-bold text-center mb-12">为什么选择我们</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {features.map((feature, index) => (
              <div 
                key={index} 
                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 text-center"
              >
                <div className="h-16 w-16 mx-auto bg-blue-100 rounded-2xl flex items-center justify-center mb-4">
                  <feature.icon className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* 推荐导师 */}
        <div className="py-12">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold">推荐导师</h2>
            <Link 
              to="/tutors" 
              className="text-blue-600 hover:text-blue-800 font-medium flex items-center mt-2 md:mt-0"
            >
              查看全部导师
              <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading ? (
              // 加载状态
              Array(3).fill(0).map((_, i) => (
                <Card key={i} className="overflow-hidden hover:shadow-lg transition-shadow duration-300 h-full">
                  <CardContent className="p-6">
                    <div className="animate-pulse space-y-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-10 bg-gray-200 rounded"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : recommendTutors.length > 0 ? (
              // 导师卡片
              recommendTutors.map((tutor) => (
                <Link to={`/tutors/${tutor.id}`} key={tutor.id} className="block h-full">
                  <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300 h-full">
                    <CardContent className="p-6">
                      <div className="flex items-start">
                        <div className="text-5xl mr-5">{tutor.avatar || '👨‍🏫'}</div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="text-lg font-semibold">{tutor.name || '未命名导师'}</h3>
                              <div className="flex items-center text-sm text-gray-600 mb-3">
                                <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                                {tutor.rating || 5} · 已辅导 {tutor.students || 0}人
                              </div>
                            </div>
                            <Button size="sm" className="whitespace-nowrap">
                              立即预约
                            </Button>
                          </div>
                          <div className="flex flex-wrap gap-2 mt-3">
                            {(tutor.tags || []).slice(0, 3).map((tag, i) => (
                              <span
                                key={i}
                                className="inline-block bg-gray-100 text-sm px-3 py-1 rounded-full"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                          {tutor.university && tutor.major && (
                            <div className="mt-3 text-sm text-gray-600">
                              {tutor.university} · {tutor.major}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              ))
            ) : (
              <div className="col-span-full text-center py-8 text-gray-500">
                暂无推荐导师
              </div>
            )}
          </div>
        </div>

        {/* 快捷入口 */}
        <div className="py-12">
          <h2 className="text-2xl md:text-3xl font-bold mb-8">快速开始</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link to="/tutors" className="group">
              <Card className="h-full border-0 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-6 text-center">
                  <div className="h-16 w-16 mx-auto bg-blue-100 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-blue-200 transition-colors duration-300">
                    <Users className="h-8 w-8 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-1">找导师</h3>
                  <p className="text-sm text-gray-600">专业导师1v1指导</p>
                </CardContent>
              </Card>
            </Link>
            <Link to="/appointments" className="group">
              <Card className="h-full border-0 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-6 text-center">
                  <div className="h-16 w-16 mx-auto bg-green-100 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-green-200 transition-colors duration-300">
                    <BookOpen className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-1">志愿填报</h3>
                  <p className="text-sm text-gray-600">科学填报志愿方案</p>
                </CardContent>
              </Card>
            </Link>
            <Link to="/tutor-register" className="group">
              <Card className="h-full border-0 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-6 text-center">
                  <div className="h-16 w-16 mx-auto bg-purple-100 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-purple-200 transition-colors duration-300">
                    <Users className="h-8 w-8 text-purple-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-1">成为导师</h3>
                  <p className="text-sm text-gray-600">分享经验，帮助他人</p>
                </CardContent>
              </Card>
            </Link>
            <Link to="/feedback" className="group">
              <Card className="h-full border-0 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                <CardContent className="p-6 text-center">
                  <div className="h-16 w-16 mx-auto bg-amber-100 rounded-2xl flex items-center justify-center mb-4 group-hover:bg-amber-200 transition-colors duration-300">
                    <Star className="h-8 w-8 text-amber-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-1">意见反馈</h3>
                  <p className="text-sm text-gray-600">您的建议对我们很重要</p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </div>
      </div>
      
      {/* 移动端底部导航栏 - 由 ResponsiveNavbar 组件处理 */}
    </div>
  );
};

export default Index;

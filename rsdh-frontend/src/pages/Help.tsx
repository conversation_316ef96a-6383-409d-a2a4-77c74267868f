import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

const Help = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1); // Go back to the previous page
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={handleBack}
              className="mr-2"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold">帮助与反馈</h1>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">常见问题</h2>
          
          <div className="space-y-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">1. 如何预约导师？</h3>
              <p className="text-gray-600">
                在导师页面选择您心仪的导师，点击"立即预约"按钮，选择合适的时间段并确认预约信息即可。
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">2. 如何取消预约？</h3>
              <p className="text-gray-600">
                在"我的预约"页面找到对应的预约记录，点击"取消预约"按钮即可。请注意，距离预约时间不足2小时将无法取消。
              </p>
            </div>
            
            <div>
              <h3 className="font-medium text-gray-900 mb-2">3. 如何联系客服？</h3>
              <p className="text-gray-600">
                您可以通过页面底部的"联系客服"按钮或发送邮件至 <EMAIL> 联系我们的客服团队，我们将在24小时内回复您。
              </p>
            </div>
          </div>
          
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">意见反馈</h2>
            <p className="text-gray-600 mb-4">
              如果您有任何意见或建议，欢迎随时告诉我们，我们将不断改进我们的服务。
            </p>
            <Button variant="outline" className="w-full">
              提交反馈
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Help;

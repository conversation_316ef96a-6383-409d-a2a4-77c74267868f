import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Camera, User, Mail, Smartphone, Lock, Shield } from "lucide-react";
import { Link } from "react-router-dom";
import { useProfile, useUpdateProfile } from "@/hooks/useProfile";

// 模拟当前用户ID - 在实际应用中这应该来自认证系统
const CURRENT_USER_ID = "mock-user-id";

const Profile = () => {
  const { data: profile, isLoading } = useProfile(CURRENT_USER_ID);
  const { mutate: updateProfile, isPending } = useUpdateProfile();

  const [formData, setFormData] = useState({
    phone: '',
    avatar_url: '',
    email: ''
  });

  useEffect(() => {
    if (profile) {
      setFormData({
        phone: profile.phone || '',
        avatar_url: profile.avatar_url || '',
        email: profile.email || ''
      });
    }
  }, [profile]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    updateProfile({
      userId: CURRENT_USER_ID,
      ...formData
    });
  };

  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 在实际应用中，这里应该上传文件到服务器并获取URL
      const mockUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, avatar_url: mockUrl }));
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/my" className="mr-4">
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </Link>
              <h1 className="text-lg font-semibold">个人资料</h1>
            </div>
            <div className="hidden lg:block">
              <Button 
                onClick={handleSubmit}
                disabled={isPending}
                className="flex items-center space-x-1"
              >
                {isPending ? '保存中...' : '保存修改'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧栏 - 头像和基本信息 */}
          <div className="lg:col-span-1">
            <Card className="h-full border-0 shadow-sm">
              <CardContent className="p-6 text-center">
                <div className="relative mx-auto w-32 h-32 mb-4">
                  <div className="w-full h-full bg-blue-100 rounded-full flex items-center justify-center overflow-hidden">
                    {formData.avatar_url ? (
                      <img 
                        src={formData.avatar_url} 
                        alt="头像" 
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <User className="h-16 w-16 text-blue-600" />
                    )}
                  </div>
                  <label className="absolute bottom-0 right-0 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center cursor-pointer hover:bg-blue-600 transition-colors">
                    <Camera className="h-4 w-4 text-white" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarUpload}
                      className="hidden"
                    />
                  </label>
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-1">{profile?.name || '用户'}</h2>
                <p className="text-sm text-gray-500">{profile?.email || '未设置邮箱'}</p>
                <p className="text-sm text-gray-500 mt-1">注册用户</p>
              </CardContent>
            </Card>
          </div>
          
          {/* 右侧内容 */}
          <div className="lg:col-span-2 space-y-4">
            {/* 基本信息 */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <User className="h-5 w-5 mr-2 text-blue-500" />
                  基本信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="phone" className="flex items-center text-sm font-medium text-gray-700 mb-1">
                    <Smartphone className="h-4 w-4 mr-1 text-gray-500" />
                    手机号码
                  </Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="请输入手机号码"
                    className="mt-1"
                  />
                </div>
              </CardContent>
            </Card>

            {/* 账号安全 */}
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <Shield className="h-5 w-5 mr-2 text-green-500" />
                  账号安全
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm flex items-center">
                      <Lock className="h-4 w-4 mr-2 text-gray-500" />
                      登录密码
                    </p>
                    <p className="text-xs text-gray-500 mt-1">定期更换密码保护账号安全</p>
                  </div>
                  <Button variant="outline" size="sm" disabled>
                    修改密码
                  </Button>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm flex items-center">
                      <Smartphone className="h-4 w-4 mr-2 text-gray-500" />
                      手机验证
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formData.phone ? '已绑定手机' : '未绑定手机'}
                    </p>
                  </div>
                  <Button 
                    variant={formData.phone ? 'outline' : 'default'}
                    size="sm" 
                    disabled={!formData.phone}
                  >
                    {formData.phone ? '更换手机' : '请先绑定手机'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 账号操作 */}
            <Card className="border-0 shadow-sm border-red-100">
              <CardHeader>
                <CardTitle className="text-base text-red-500">
                  账号操作
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button 
                    variant="ghost" 
                    className="w-full justify-start text-red-500 hover:bg-red-50 hover:text-red-600"
                    disabled
                  >
                    注销账号
                  </Button>
                  <p className="text-xs text-gray-500 px-2">
                    注销后将无法恢复账号数据，请谨慎操作
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* 移动端保存按钮 */}
            <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t p-4 z-50">
              <Button 
                onClick={handleSubmit}
                className="w-full h-12"
                disabled={isPending}
              >
                {isPending ? '保存中...' : '保存修改'}
              </Button>
            </div>
            <div className="lg:hidden h-16"></div> {/* 为移动端底部按钮留出空间 */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;


import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

const UserAgreement = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
        <div className="max-w-4xl mx-auto flex items-center">
          <Link to="/login" className="mr-4">
            <ArrowLeft className="h-6 w-6 text-gray-600" />
          </Link>
          <h1 className="text-lg font-semibold">用户协议</h1>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4">
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-center text-xl">人生领航员用户服务协议</CardTitle>
            <p className="text-center text-sm text-gray-500">生效日期：2024年6月10日</p>
          </CardHeader>
          <CardContent className="space-y-6 text-sm leading-relaxed">
            <section>
              <h3 className="text-base font-semibold mb-3">第一条 协议的范围</h3>
              <p>本协议是您与人生领航员平台之间关于您注册、登录、使用人生领航员平台服务所订立的协议。</p>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第二条 服务内容</h3>
              <p>人生领航员平台致力于为用户提供专业的志愿填报指导、升学规划、职业规划等教育咨询服务，包括但不限于：</p>
              <ul className="list-disc list-inside mt-2 space-y-1 ml-4">
                <li>优质导师匹配服务</li>
                <li>一对一志愿填报指导</li>
                <li>职业规划咨询</li>
                <li>升学路径规划</li>
                <li>相关教育资讯分享</li>
              </ul>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第三条 用户注册</h3>
              <div className="space-y-2">
                <p>3.1 用户承诺以真实身份注册成为平台用户，确保所提供的个人身份资料信息真实、完整、有效。</p>
                <p>3.2 用户有义务适时更新注册资料，符合及时、详尽准确的要求。</p>
                <p>3.3 平台有权审核用户所提供的身份信息是否真实、有效，并应积极地采取技术与管理等合理措施保障用户账户的安全、有效。</p>
              </div>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第四条 用户权利和义务</h3>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium">4.1 用户权利：</h4>
                  <ul className="list-disc list-inside mt-1 space-y-1 ml-4">
                    <li>享受平台提供的各项服务</li>
                    <li>对服务质量进行评价和监督</li>
                    <li>要求平台保护个人隐私信息</li>
                    <li>在法律允许范围内转让账户</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium">4.2 用户义务：</h4>
                  <ul className="list-disc list-inside mt-1 space-y-1 ml-4">
                    <li>遵守相关法律法规</li>
                    <li>维护平台正常运营秩序</li>
                    <li>不得恶意评价或诋毁导师</li>
                    <li>按约定支付相关费用</li>
                    <li>保护账户安全，不得转借他人使用</li>
                  </ul>
                </div>
              </div>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第五条 服务费用</h3>
              <div className="space-y-2">
                <p>5.1 平台提供的部分服务可能需要用户支付相应费用，具体收费标准以页面展示为准。</p>
                <p>5.2 用户应按照平台规定的支付方式及时支付费用。</p>
                <p>5.3 如因服务质量问题需要退费的，按照平台退费政策执行。</p>
              </div>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第六条 知识产权</h3>
              <p>平台所提供的服务、软件、技术及相关的所有内容的知识产权均归平台或其授权方所有。未经平台书面同意，用户不得擅自复制、传播、转让、许可或提供他人使用。</p>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第七条 免责声明</h3>
              <div className="space-y-2">
                <p>7.1 平台仅提供信息服务平台，不对最终录取结果承担责任。</p>
                <p>7.2 因不可抗力等因素导致的服务中断，平台不承担责任。</p>
                <p>7.3 用户因使用平台服务而产生的任何纠纷，应首先协商解决。</p>
              </div>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第八条 协议修改</h3>
              <p>平台有权根据需要修改本协议条款。协议修改后，平台将在网站显著位置提示修改内容。如果不同意修改内容，用户可以主动取消获得的服务；如果用户继续享用服务，则视为接受协议修改。</p>
            </section>

            <section>
              <h3 className="text-base font-semibold mb-3">第九条 争议解决</h3>
              <p>本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。如双方就本协议内容或其执行发生任何争议，双方应尽量友好协商解决；协商不成时，任何一方均可向平台所在地的人民法院起诉。</p>
            </section>

            <div className="text-center pt-6 border-t">
              <p className="text-gray-600 text-sm">人生领航员平台</p>
              <p className="text-gray-500 text-xs mt-1">本协议最终解释权归人生领航员平台所有</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UserAgreement;


import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface TutorRegistrationData {
  name: string;
  phone: string;
  email: string;
  gaokaoScore: string;
  gaokaoYear: string;
  university: string;
  major: string;
  degree: string;
  graduationYear: string;
  company: string;
  position: string;
  workYears: string;
  industry: string;
  specialization: string;
  introduction: string;
}

export const useTutorRegister = () => {
  return useMutation({
    mutationFn: async (data: TutorRegistrationData) => {
      console.log('Submitting tutor registration:', data);

      // 插入导师基本信息
      const { data: tutorData, error: tutorError } = await supabase
        .from('tutors')
        .insert({
          name: data.name,
          university: data.university,
          major: data.major,
          graduation_year: parseInt(data.graduationYear),
          current_job: data.position,
          company: data.company,
          province: '北京', // 默认省份，可以后续添加选择
          gaokao_score: parseInt(data.gaokaoScore),
          gaokao_province: '北京', // 默认省份
          bio: data.introduction,
          specialties: [data.specialization],
          tags: [data.industry],
          price: 299, // 默认价格
          is_active: false // 待审核状态
        })
        .select()
        .single();

      if (tutorError) {
        console.error('Error creating tutor:', tutorError);
        throw new Error('提交失败，请重试');
      }

      // 插入教育经历
      const { error: educationError } = await supabase
        .from('tutor_education')
        .insert({
          tutor_id: tutorData.id,
          degree: data.degree,
          school: data.university,
          major: data.major,
          start_year: parseInt(data.graduationYear) - 4,
          end_year: parseInt(data.graduationYear)
        });

      if (educationError) {
        console.error('Error creating education:', educationError);
      }

      // 插入职业经历
      const { error: careerError } = await supabase
        .from('tutor_career')
        .insert({
          tutor_id: tutorData.id,
          company: data.company,
          position: data.position,
          start_date: new Date().toISOString().split('T')[0], // 当前日期
          is_current: true
        });

      if (careerError) {
        console.error('Error creating career:', careerError);
      }

      return tutorData;
    },
    onSuccess: () => {
      toast.success('申请提交成功！我们将在3个工作日内完成审核');
    },
    onError: (error) => {
      console.error('Registration error:', error);
      toast.error(error instanceof Error ? error.message : '提交失败，请重试');
    },
  });
};


import { useMutation } from '@tanstack/react-query';
import { apiClient, TutorApplication } from '@/lib/api';
import { toast } from 'sonner';

interface TutorRegistrationData {
  title: string;
  bio: string;
  rate: number;
  university: string;
  major: string;
  degree: string;
  graduationYear: string;
  company: string;
  position: string;
  workYears: string;
}

export const useTutorRegister = () => {
  return useMutation({
    mutationFn: async (data: TutorRegistrationData) => {
      console.log('Submitting tutor registration:', data);

      const applicationData: TutorApplication = {
        title: data.title,
        bio: data.bio,
        rate: data.rate,
        education: [{
          degree: data.degree,
          fieldOfStudy: data.major,
          institution: data.university,
          startYear: parseInt(data.graduationYear) - 4,
          endYear: parseInt(data.graduationYear),
        }],
        career: [{
          company: data.company,
          position: data.position,
          startDate: new Date(Date.now() - parseInt(data.workYears) * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
          isCurrent: true,
        }],
      };

      return await apiClient.post('/tutors/apply', applicationData);
    },
    onSuccess: () => {
      toast.success('导师申请提交成功，请等待审核');
    },
    onError: (error) => {
      console.error('Registration error:', error);
      toast.error(error instanceof Error ? error.message : '提交失败，请重试');
    },
  });
};

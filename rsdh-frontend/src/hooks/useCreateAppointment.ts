
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, CreateAppointmentRequest, Appointment } from '@/lib/api';
import { toast } from 'sonner';

// Use the CreateAppointmentRequest type from API

export const useCreateAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateAppointmentRequest): Promise<Appointment> => {
      console.log('Creating appointment:', data);
      return await apiClient.post<Appointment>('/appointments', data);
    },
    onSuccess: (appointment) => {
      queryClient.invalidateQueries({ queryKey: ['user-appointments'] });
      queryClient.invalidateQueries({ queryKey: ['tutor-appointments'] });
      console.log('Appointment created and tutor notified:', appointment.id);
    },
    onError: (error) => {
      console.error('Create appointment error:', error);
      toast.error('预约创建失败，请重试');
    },
  });
};


import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface CreateAppointmentData {
  tutor_id: string;
  student_id: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  duration: number;
  price: number;
  meeting_type: 'video' | 'voice' | 'offline';
  location?: string;
  notes?: string;
}

export const useCreateAppointment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateAppointmentData) => {
      console.log('Creating appointment:', data);

      const { data: appointment, error } = await supabase
        .from('appointments')
        .insert({
          tutor_id: data.tutor_id,
          student_id: data.student_id,
          appointment_date: data.appointment_date,
          start_time: data.start_time,
          end_time: data.end_time,
          duration: data.duration,
          price: data.price,
          meeting_type: data.meeting_type,
          location: data.location || null,
          notes: data.notes || null,
          status: 'pending'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating appointment:', error);
        throw error;
      }

      // 模拟通知导师（在实际应用中这里会调用通知服务）
      console.log(`Notifying tutor ${data.tutor_id} about new appointment ${appointment.id}`);
      
      return appointment;
    },
    onSuccess: (appointment) => {
      queryClient.invalidateQueries({ queryKey: ['user-appointments'] });
      queryClient.invalidateQueries({ queryKey: ['tutor-appointments'] });
      console.log('Appointment created and tutor notified:', appointment.id);
    },
    onError: (error) => {
      console.error('Create appointment error:', error);
      toast.error('预约创建失败，请重试');
    },
  });
};


import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';

interface NotifyTutorData {
  tutorId: string;
  appointmentId: string;
  studentName?: string;
  appointmentDate: string;
  appointmentTime: string;
}

export const useNotifyTutor = () => {
  return useMutation({
    mutationFn: async (data: NotifyTutorData) => {
      console.log('Notifying tutor:', data);
      
      // 模拟通知导师的API调用
      // 在实际应用中，这里会调用后端API发送邮件/短信通知
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟通知成功
      return { success: true, notificationId: `notif_${Date.now()}` };
    },
    onSuccess: () => {
      console.log('Tutor notification sent successfully');
      toast.success('已通知导师，请等待确认');
    },
    onError: (error) => {
      console.error('Failed to notify tutor:', error);
      toast.error('通知导师失败，但预约已创建');
    },
  });
};

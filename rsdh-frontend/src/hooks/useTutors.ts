
import { useQuery } from '@tanstack/react-query';
import { apiClient, Tutor, PaginatedResponse } from '@/lib/api';

export const useTutors = (filters?: {
  searchText?: string;
  page?: number;
  limit?: number;
}) => {
  return useQuery({
    queryKey: ['tutors', filters],
    queryFn: async (): Promise<PaginatedResponse<Tutor>> => {
      const params = new URLSearchParams();

      if (filters?.searchText) {
        params.append('search', filters.searchText);
      }
      if (filters?.page) {
        params.append('page', filters.page.toString());
      }
      if (filters?.limit) {
        params.append('limit', filters.limit.toString());
      }

      const queryString = params.toString();
      const endpoint = queryString ? `/tutors?${queryString}` : '/tutors';

      return await apiClient.get<PaginatedResponse<Tutor>>(endpoint);
    },
  });
};

export const useTutor = (tutorId: string) => {
  return useQuery({
    queryKey: ['tutor', tutorId],
    queryFn: async (): Promise<Tutor> => {
      return await apiClient.get<Tutor>(`/tutors/${tutorId}`);
    },
    enabled: !!tutorId,
  });
};

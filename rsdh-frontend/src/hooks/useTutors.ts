
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Tu<PERSON> } from '@/types/database';

export const useTutors = (filters?: {
  searchText?: string;
  province?: string;
  university?: string;
  year?: string;
}) => {
  return useQuery({
    queryKey: ['tutors', filters],
    queryFn: async (): Promise<Tutor[]> => {
      let query = supabase
        .from('tutors')
        .select('*')
        .eq('is_active', true);

      if (filters?.searchText) {
        query = query.or(`name.ilike.%${filters.searchText}%,major.ilike.%${filters.searchText}%,university.ilike.%${filters.searchText}%`);
      }

      if (filters?.province && filters.province !== 'all') {
        query = query.eq('gaokao_province', filters.province);
      }

      if (filters?.university && filters.university !== 'all') {
        query = query.eq('university', filters.university);
      }

      if (filters?.year && filters.year !== 'all') {
        query = query.eq('graduation_year', parseInt(filters.year));
      }

      const { data, error } = await query.order('rating', { ascending: false });

      if (error) {
        console.error('Error fetching tutors:', error);
        throw error;
      }

      return data || [];
    },
  });
};

export const useTutor = (tutorId: string) => {
  return useQuery({
    queryKey: ['tutor', tutorId],
    queryFn: async (): Promise<Tutor | null> => {
      const { data, error } = await supabase
        .from('tutors')
        .select('*')
        .eq('id', tutorId)
        .single();

      if (error) {
        console.error('Error fetching tutor:', error);
        throw error;
      }

      return data;
    },
    enabled: !!tutorId,
  });
};

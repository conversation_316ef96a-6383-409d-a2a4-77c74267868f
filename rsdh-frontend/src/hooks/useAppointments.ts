
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Appointment } from '@/types/database';

export const useAppointments = (userId?: string) => {
  return useQuery({
    queryKey: ['appointments', userId],
    queryFn: async (): Promise<Appointment[]> => {
      console.log('Fetching appointments for user:', userId);
      
      let query = supabase
        .from('appointments')
        .select(`
          *,
          tutor:tutors(*)
        `);

      if (userId) {
        query = query.eq('student_id', userId);
      }

      const { data, error } = await query.order('appointment_date', { ascending: true });

      if (error) {
        console.error('Error fetching appointments:', error);
        throw error;
      }

      // 确保返回的数据符合 Appointment 类型
      return (data || []).map(appointment => ({
        ...appointment,
        tutor: appointment.tutor || undefined
      })) as Appointment[];
    },
    enabled: !!userId,
  });
};

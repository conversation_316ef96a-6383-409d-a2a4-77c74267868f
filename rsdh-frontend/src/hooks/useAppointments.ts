
import { useQuery } from '@tanstack/react-query';
import { apiClient, Appointment, PaginatedResponse } from '@/lib/api';

export const useAppointments = (filters?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  return useQuery({
    queryKey: ['appointments', filters],
    queryFn: async (): Promise<PaginatedResponse<Appointment>> => {
      console.log('Fetching appointments with filters:', filters);

      const params = new URLSearchParams();

      if (filters?.page) {
        params.append('page', filters.page.toString());
      }
      if (filters?.limit) {
        params.append('limit', filters.limit.toString());
      }
      if (filters?.status) {
        params.append('status', filters.status);
      }

      const queryString = params.toString();
      const endpoint = queryString ? `/appointments?${queryString}` : '/appointments';

      return await apiClient.get<PaginatedResponse<Appointment>>(endpoint);
    },
  });
};

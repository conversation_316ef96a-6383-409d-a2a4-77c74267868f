
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useAdminStats = () => {
  return useQuery({
    queryKey: ['admin-stats'],
    queryFn: async () => {
      console.log('Fetching admin statistics');

      // 获取总导师数
      const { count: tutorCount, error: tutorError } = await supabase
        .from('tutors')
        .select('*', { count: 'exact', head: true });

      if (tutorError) {
        console.error('Error fetching tutor count:', tutorError);
      }

      // 获取本月咨询数
      const currentMonth = new Date();
      const firstDayOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      
      const { count: appointmentCount, error: appointmentError } = await supabase
        .from('appointments')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', firstDayOfMonth.toISOString());

      if (appointmentError) {
        console.error('Error fetching appointment count:', appointmentError);
      }

      // 获取待审核导师数
      const { count: pendingTutorCount, error: pendingError } = await supabase
        .from('tutors')
        .select('*', { count: 'exact', head: true })
        .eq('is_active', false);

      if (pendingError) {
        console.error('Error fetching pending tutors:', pendingError);
      }

      // 获取平均评分
      const { data: ratingData, error: ratingError } = await supabase
        .from('tutors')
        .select('rating')
        .not('rating', 'is', null);

      if (ratingError) {
        console.error('Error fetching ratings:', ratingError);
      }

      const averageRating = ratingData && ratingData.length > 0 
        ? ratingData.reduce((sum, tutor) => sum + (tutor.rating || 0), 0) / ratingData.length
        : 0;

      return {
        tutorCount: tutorCount || 0,
        appointmentCount: appointmentCount || 0,
        pendingTutorCount: pendingTutorCount || 0,
        averageRating: Math.round(averageRating * 10) / 10
      };
    },
  });
};

export const useWarningTutors = () => {
  return useQuery({
    queryKey: ['warning-tutors'],
    queryFn: async () => {
      console.log('Fetching warning tutors');

      // 获取评分较低的导师
      const { data: lowRatingTutors, error } = await supabase
        .from('tutors')
        .select('id, name, rating, review_count')
        .lt('rating', 4.2)
        .gt('review_count', 2)
        .order('rating', { ascending: true })
        .limit(10);

      if (error) {
        console.error('Error fetching warning tutors:', error);
        return [];
      }

      return lowRatingTutors.map(tutor => ({
        id: tutor.id,
        name: tutor.name,
        rating: tutor.rating || 0,
        complaints: Math.floor(Math.random() * 5) + 1, // 模拟投诉数
        status: tutor.rating && tutor.rating < 4.0 ? '待封禁' : '警告'
      }));
    },
  });
};

export const useProvinceStats = () => {
  return useQuery({
    queryKey: ['province-stats'],
    queryFn: async () => {
      console.log('Fetching province statistics');

      // 获取各省份的咨询量统计
      const { data: appointments, error } = await supabase
        .from('appointments')
        .select(`
          id,
          tutors (
            gaokao_province
          )
        `)
        .not('tutors.gaokao_province', 'is', null);

      if (error) {
        console.error('Error fetching province stats:', error);
        return [];
      }

      // 统计各省份咨询量
      const provinceCount: Record<string, number> = {};
      appointments?.forEach(appointment => {
        const province = appointment.tutors?.gaokao_province;
        if (province) {
          provinceCount[province] = (provinceCount[province] || 0) + 1;
        }
      });

      // 转换为数组并排序
      return Object.entries(provinceCount)
        .map(([province, consultations]) => ({ province, consultations }))
        .sort((a, b) => b.consultations - a.consultations)
        .slice(0, 5);
    },
  });
};

export const useIndustryStats = () => {
  return useQuery({
    queryKey: ['industry-stats'],
    queryFn: async () => {
      console.log('Fetching industry statistics');

      // 获取行业分布
      const { data: tutors, error } = await supabase
        .from('tutors')
        .select('tags')
        .not('tags', 'is', null);

      if (error) {
        console.error('Error fetching industry stats:', error);
        return [];
      }

      // 统计各行业数量
      const industryCount: Record<string, number> = {};
      tutors?.forEach(tutor => {
        tutor.tags?.forEach(tag => {
          industryCount[tag] = (industryCount[tag] || 0) + 1;
        });
      });

      const total = Object.values(industryCount).reduce((sum, count) => sum + count, 0);
      
      return Object.entries(industryCount)
        .map(([industry, count]) => ({
          industry,
          count,
          percentage: total > 0 ? Math.round((count / total) * 100) : 0
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);
    },
  });
};

export const useLowRatingReviews = () => {
  return useQuery({
    queryKey: ['low-rating-reviews'],
    queryFn: async () => {
      console.log('Fetching low rating reviews');

      // 获取低分评价
      const { data: reviews, error } = await supabase
        .from('reviews')
        .select(`
          id,
          rating,
          content,
          created_at,
          tutors (
            name
          )
        `)
        .lte('rating', 3)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('Error fetching low rating reviews:', error);
        return [];
      }

      return reviews?.map(review => ({
        id: review.id,
        student: '匿名学生', // 保护学生隐私
        tutor: review.tutors?.name || '未知导师',
        rating: review.rating,
        comment: review.content || '无评价内容',
        date: new Date(review.created_at || '').toLocaleDateString()
      })) || [];
    },
  });
};


import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, User } from '@/lib/api';
import { toast } from 'sonner';

export const useProfile = () => {
  return useQuery({
    queryKey: ['profile'],
    queryFn: async (): Promise<User> => {
      console.log('Fetching current user profile');
      return await apiClient.get<User>('/users/profile');
    },
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { name?: string; avatar?: string }) => {
      console.log('Updating profile:', data);
      return await apiClient.put<User>('/users/profile', data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['profile'] });
      toast.success('个人信息更新成功');
    },
    onError: (error) => {
      console.error('Profile update error:', error);
      toast.error(error instanceof Error ? error.message : '更新失败，请重试');
    },
  });
};

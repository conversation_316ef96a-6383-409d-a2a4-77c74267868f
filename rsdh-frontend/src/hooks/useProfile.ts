
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Profile } from '@/types/database';

export const useProfile = (userId?: string) => {
  return useQuery({
    queryKey: ['profile', userId],
    queryFn: async (): Promise<Profile | null> => {
      if (!userId) {
        return null;
      }

      console.log('Fetching profile for user:', userId);

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching profile:', error);
        throw error;
      }

      return data;
    },
    enabled: !!userId,
  });
};

export const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { userId: string; phone?: string; avatar_url?: string }) => {
      console.log('Updating profile:', data);

      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: data.userId,
          phone: data.phone,
          avatar_url: data.avatar_url,
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error updating profile:', error);
        throw new Error('更新失败，请重试');
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['profile', data.userId] });
      toast.success('个人信息更新成功');
    },
    onError: (error) => {
      console.error('Profile update error:', error);
      toast.error(error instanceof Error ? error.message : '更新失败，请重试');
    },
  });
};

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, Review, PaginatedResponse } from '@/lib/api';
import { toast } from 'sonner';

interface CreateReviewRequest {
  appointmentId: string;
  tutorId: string;
  rating: number;
  comment?: string;
}

export const useReviews = (filters?: {
  tutorId?: string;
  page?: number;
  limit?: number;
  rating?: number;
}) => {
  return useQuery({
    queryKey: ['reviews', filters],
    queryFn: async (): Promise<PaginatedResponse<Review>> => {
      const params = new URLSearchParams();
      
      if (filters?.tutorId) {
        params.append('tutorId', filters.tutorId);
      }
      if (filters?.page) {
        params.append('page', filters.page.toString());
      }
      if (filters?.limit) {
        params.append('limit', filters.limit.toString());
      }
      if (filters?.rating) {
        params.append('rating', filters.rating.toString());
      }

      const queryString = params.toString();
      const endpoint = queryString ? `/reviews?${queryString}` : '/reviews';
      
      return await apiClient.get<PaginatedResponse<Review>>(endpoint);
    },
  });
};

export const useCreateReview = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateReviewRequest): Promise<Review> => {
      return await apiClient.post<Review>('/reviews', data);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['reviews'] });
      queryClient.invalidateQueries({ queryKey: ['reviews', { tutorId: variables.tutorId }] });
      toast.success('评价提交成功');
    },
    onError: (error) => {
      console.error('Create review error:', error);
      toast.error(error instanceof Error ? error.message : '评价提交失败');
    },
  });
};

export const useTutorReviews = (tutorId: string) => {
  return useQuery({
    queryKey: ['tutor-reviews', tutorId],
    queryFn: async (): Promise<PaginatedResponse<Review>> => {
      return await apiClient.get<PaginatedResponse<Review>>(`/reviews?tutorId=${tutorId}`);
    },
    enabled: !!tutorId,
  });
};


import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Tu<PERSON>, TutorEducation, TutorCareer, TutorAvailability, Review } from '@/types/database';

export const useTutorDetails = (tutorId: string) => {
  return useQuery({
    queryKey: ['tutor-details', tutorId],
    queryFn: async () => {
      console.log('Fetching tutor details for ID:', tutorId);
      
      // 获取导师基本信息
      const { data: tutor, error: tutorError } = await supabase
        .from('tutors')
        .select('*')
        .eq('id', tutorId)
        .single();

      if (tutorError) {
        console.error('Error fetching tutor:', tutorError);
        throw tutorError;
      }

      // 获取教育经历
      const { data: education, error: educationError } = await supabase
        .from('tutor_education')
        .select('*')
        .eq('tutor_id', tutorId)
        .order('start_year', { ascending: false });

      if (educationError) {
        console.error('Error fetching education:', educationError);
      }

      // 获取职业经历
      const { data: career, error: careerError } = await supabase
        .from('tutor_career')
        .select('*')
        .eq('tutor_id', tutorId)
        .order('start_date', { ascending: false });

      if (careerError) {
        console.error('Error fetching career:', careerError);
      }

      // 获取可预约时间
      const { data: availability, error: availabilityError } = await supabase
        .from('tutor_availability')
        .select('*')
        .eq('tutor_id', tutorId)
        .eq('is_available', true)
        .gte('date', new Date().toISOString().split('T')[0])
        .order('date', { ascending: true });

      if (availabilityError) {
        console.error('Error fetching availability:', availabilityError);
      }

      // 获取评价
      const { data: reviews, error: reviewsError } = await supabase
        .from('reviews')
        .select('*')
        .eq('tutor_id', tutorId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (reviewsError) {
        console.error('Error fetching reviews:', reviewsError);
      }

      return {
        tutor: tutor as Tutor,
        education: (education || []) as TutorEducation[],
        career: (career || []) as TutorCareer[],
        availability: (availability || []) as TutorAvailability[],
        reviews: (reviews || []) as Review[]
      };
    },
    enabled: !!tutorId,
  });
};

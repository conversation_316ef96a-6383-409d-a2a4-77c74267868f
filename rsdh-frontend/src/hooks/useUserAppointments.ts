
import { useQuery } from '@tanstack/react-query';
import { apiClient, Appointment, PaginatedResponse } from '@/lib/api';

export const useUserAppointments = (filters?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  return useQuery({
    queryKey: ['user-appointments', filters],
    queryFn: async (): Promise<PaginatedResponse<Appointment>> => {
      console.log('Fetching user appointments with filters:', filters);

      const params = new URLSearchParams();

      if (filters?.page) {
        params.append('page', filters.page.toString());
      }
      if (filters?.limit) {
        params.append('limit', filters.limit.toString());
      }
      if (filters?.status) {
        params.append('status', filters.status);
      }

      const queryString = params.toString();
      const endpoint = queryString ? `/appointments/my?${queryString}` : '/appointments/my';

      return await apiClient.get<PaginatedResponse<Appointment>>(endpoint);
    },
  });
};

export const useTutorAppointments = (tutorId?: string) => {
  return useQuery({
    queryKey: ['tutor-appointments', tutorId],
    queryFn: async (): Promise<Appointment[]> => {
      if (!tutorId) {
        return [];
      }

      console.log('Fetching tutor appointments for tutor:', tutorId);

      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('tutor_id', tutorId)
        .order('appointment_date', { ascending: false });

      if (error) {
        console.error('Error fetching tutor appointments:', error);
        throw error;
      }

      return (data || []).map(appointment => ({
        ...appointment,
        status: appointment.status as 'pending' | 'confirmed' | 'completed' | 'cancelled',
        meeting_type: appointment.meeting_type as 'video' | 'voice' | 'offline'
      })) as Appointment[];
    },
    enabled: !!tutorId,
  });
};

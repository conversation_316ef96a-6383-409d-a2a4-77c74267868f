
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Appointment } from '@/types/database';

export const useUserAppointments = (userId?: string) => {
  return useQuery({
    queryKey: ['user-appointments', userId],
    queryFn: async (): Promise<Appointment[]> => {
      if (!userId) {
        return [];
      }

      console.log('Fetching user appointments for user:', userId);

      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          tutors (
            id,
            name,
            university,
            major
          )
        `)
        .eq('student_id', userId)
        .order('appointment_date', { ascending: false });

      if (error) {
        console.error('Error fetching user appointments:', error);
        throw error;
      }

      return (data || []).map(appointment => ({
        ...appointment,
        status: appointment.status as 'pending' | 'confirmed' | 'completed' | 'cancelled',
        meeting_type: appointment.meeting_type as 'video' | 'voice' | 'offline'
      })) as Appointment[];
    },
    enabled: !!userId,
  });
};

export const useTutorAppointments = (tutorId?: string) => {
  return useQuery({
    queryKey: ['tutor-appointments', tutorId],
    queryFn: async (): Promise<Appointment[]> => {
      if (!tutorId) {
        return [];
      }

      console.log('Fetching tutor appointments for tutor:', tutorId);

      const { data, error } = await supabase
        .from('appointments')
        .select('*')
        .eq('tutor_id', tutorId)
        .order('appointment_date', { ascending: false });

      if (error) {
        console.error('Error fetching tutor appointments:', error);
        throw error;
      }

      return (data || []).map(appointment => ({
        ...appointment,
        status: appointment.status as 'pending' | 'confirmed' | 'completed' | 'cancelled',
        meeting_type: appointment.meeting_type as 'video' | 'voice' | 'offline'
      })) as Appointment[];
    },
    enabled: !!tutorId,
  });
};

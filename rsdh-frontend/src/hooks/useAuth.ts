import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiClient, tokenManager, LoginRequest, RegisterRequest, SendCodeRequest, ResetPasswordRequest, User } from '@/lib/api';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  sendCode: (data: SendCodeRequest) => Promise<void>;
  resetPassword: (data: ResetPasswordRequest) => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!tokenManager.getToken();

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      const token = tokenManager.getToken();
      if (token) {
        try {
          await refreshUser();
        } catch (error) {
          console.error('Failed to refresh user:', error);
          tokenManager.removeToken();
        }
      }
      setIsLoading(false);
    };

    initAuth();
  }, []);

  const login = async (credentials: LoginRequest) => {
    try {
      const response = await apiClient.post<any>('/auth/login', credentials);

      tokenManager.setToken(response.token);
      setUser(response.user);

      toast.success('登录成功');
    } catch (error) {
      console.error('Login failed:', error);
      toast.error(error instanceof Error ? error.message : '登录失败');
      throw error;
    }
  };

  const register = async (data: RegisterRequest) => {
    try {
      await apiClient.post('/auth/register-with-code', data);
      toast.success('注册成功，请登录');
    } catch (error) {
      console.error('Registration failed:', error);
      toast.error(error instanceof Error ? error.message : '注册失败');
      throw error;
    }
  };

  const logout = () => {
    tokenManager.removeToken();
    setUser(null);
    toast.success('已退出登录');
  };

  const sendCode = async (data: SendCodeRequest) => {
    try {
      await apiClient.post('/auth/send-code', data);
      toast.success('验证码已发送');
    } catch (error) {
      console.error('Send code failed:', error);
      toast.error(error instanceof Error ? error.message : '发送验证码失败');
      throw error;
    }
  };

  const resetPassword = async (data: ResetPasswordRequest) => {
    try {
      await apiClient.post('/auth/reset-password-with-code', data);
      toast.success('密码重置成功');
    } catch (error) {
      console.error('Reset password failed:', error);
      toast.error(error instanceof Error ? error.message : '密码重置失败');
      throw error;
    }
  };

  const refreshUser = async () => {
    try {
      const userData = await apiClient.get<User>('/users/profile');
      setUser(userData);
    } catch (error) {
      console.error('Failed to refresh user:', error);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    sendCode,
    resetPassword,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

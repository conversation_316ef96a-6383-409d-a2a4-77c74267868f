// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wysxywmxavtapbbnwtfo.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind5c3h5d214YXZ0YXBiYm53dGZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAwMzk3NjMsImV4cCI6MjA2NTYxNTc2M30.49bPIi5aBtoOLQMGH5YjW6-a6jRXKue4u-v95g7PRIk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
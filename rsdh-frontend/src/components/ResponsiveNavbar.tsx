import { Link, useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { Button } from "@/components/ui/button";

const ResponsiveNavbar = () => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const location = useLocation();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const navItems = [
    { path: "/", icon: "🏠", label: "首页" },
    { path: "/tutors", icon: "👥", label: "导师库" },
    { path: "/appointments", icon: "📅", label: "预约" },
    { path: "/my", icon: "👤", label: "我的" },
  ];

  const isActive = (path: string) => location.pathname === path;

  // 移动端底部导航
  if (isMobile) {
    return (
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
        <div className="max-w-md mx-auto flex">
          {navItems.map((item) => (
            <Link 
              key={item.path} 
              to={item.path} 
              className="flex-1 py-3 text-center"
            >
              <div className={isActive(item.path) ? 'text-blue-500' : 'text-gray-500'}>
                <div className="h-6 w-6 mx-auto mb-1">{item.icon}</div>
                <span className="text-xs">{item.label}</span>
              </div>
            </Link>
          ))}
        </div>
      </div>
    );
  }

  // 桌面端顶部导航
  return (
    <div className="bg-white shadow-sm px-4 py-3 sticky top-0 z-50">
      <div className="max-w-4xl mx-auto flex items-center justify-between">
        <h1 className="text-lg font-bold text-blue-600">人生领航员</h1>
        <div className="flex items-center space-x-4">
          <nav className="flex space-x-6">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium ${
                  isActive(item.path)
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                }`}
              >
                <span className="text-base mr-1">{item.icon}</span>
                <span>{item.label}</span>
              </Link>
            ))}
          </nav>
          <Link to="/login">
            <Button variant="outline" size="sm">登录</Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ResponsiveNavbar;

// Simple test script to verify API functionality
const API_BASE = 'http://localhost:3002/api';

async function testSendCode() {
  try {
    console.log('Testing send verification code...');

    const response = await fetch(`${API_BASE}/auth/send-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        type: 'registration'
      })
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ Send code successful:', data);
      return true;
    } else {
      console.log('❌ Send code failed:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Send code error:', error);
    return false;
  }
}

async function testRegister() {
  try {
    console.log('Testing registration...');

    const response = await fetch(`${API_BASE}/auth/register-with-code`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        code: '123456', // This will likely fail, but let's see the response
        password: 'testpassword123',
        name: 'Test User'
      })
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ Registration successful:', data);
      return true;
    } else {
      console.log('❌ Registration failed:', data);
      return false;
    }
  } catch (error) {
    console.error('❌ Registration error:', error);
    return false;
  }
}

async function testLogin() {
  try {
    console.log('Testing login...');

    const response = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword123'
      })
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ Login successful:', data);
      return data.token;
    } else {
      console.log('❌ Login failed:', data);
      return null;
    }
  } catch (error) {
    console.error('❌ Login error:', error);
    return null;
  }
}

async function testProfile(token) {
  try {
    console.log('Testing profile...');

    const response = await fetch(`${API_BASE}/users/profile`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    const data = await response.json();

    if (response.ok) {
      console.log('✅ Profile fetch successful:', data);
    } else {
      console.log('❌ Profile fetch failed:', data);
    }
  } catch (error) {
    console.error('❌ Profile error:', error);
  }
}

async function testTutors() {
  try {
    console.log('Testing tutors list...');

    const response = await fetch(`${API_BASE}/tutors`);
    const data = await response.json();

    if (response.ok) {
      console.log('✅ Tutors fetch successful:', data);
    } else {
      console.log('❌ Tutors fetch failed:', data);
    }
  } catch (error) {
    console.error('❌ Tutors error:', error);
  }
}

async function runTests() {
  console.log('🚀 Starting API tests...\n');

  // Test send code
  await testSendCode();
  console.log('');

  // Test registration
  await testRegister();
  console.log('');

  // Test login
  const token = await testLogin();
  console.log('');

  // Test profile if login successful
  if (token) {
    await testProfile(token);
    console.log('');
  }

  // Test tutors (public endpoint)
  await testTutors();

  console.log('\n✨ Tests completed!');
}

runTests();

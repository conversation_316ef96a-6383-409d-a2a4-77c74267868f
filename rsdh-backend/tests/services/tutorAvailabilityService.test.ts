import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { TutorAvailabilityService, CreateAvailabilityData } from '../../src/services/tutorAvailabilityService';

describe('TutorAvailabilityService', () => {
  let testUserId: string;
  let testTutorId: string;

  beforeEach(async () => {
    // Create a test user and tutor profile
    const testUser = await prisma.user.create({
      data: {
        email: `availability-test-${Date.now()}@example.com`,
        name: 'Test Tutor User',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testUserId = testUser.id;

    const tutorProfile = await prisma.tutorProfile.create({
      data: {
        userId: testUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 50,
        status: 'approved'
      }
    });
    testTutorId = tutorProfile.id;
  });

  afterEach(async () => {
    // Clean up test data in correct order
    try {
      await prisma.tutorAvailability.deleteMany({
        where: { tutorId: testTutorId }
      });

      await prisma.tutorProfile.delete({
        where: { id: testTutorId }
      });

      await prisma.user.delete({
        where: { id: testUserId }
      });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('addAvailability', () => {
    it('should add availability slot successfully', async () => {
      const availabilityData: CreateAvailabilityData = {
        tutorId: testTutorId,
        dayOfWeek: 1, // Monday
        startTime: '09:00',
        endTime: '17:00'
      };

      const availability = await TutorAvailabilityService.addAvailability(availabilityData);

      expect(availability).toBeDefined();
      expect(availability.tutorId).toBe(testTutorId);
      expect(availability.dayOfWeek).toBe(1);
      expect(availability.startTime).toBe('09:00');
      expect(availability.endTime).toBe('17:00');
    });

    it('should throw error for invalid time format', async () => {
      const availabilityData: CreateAvailabilityData = {
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '25:00', // Invalid hour
        endTime: '17:00'
      };

      await expect(TutorAvailabilityService.addAvailability(availabilityData))
        .rejects.toThrow('Invalid time format. Use HH:MM format');
    });

    it('should throw error for invalid day of week', async () => {
      const availabilityData: CreateAvailabilityData = {
        tutorId: testTutorId,
        dayOfWeek: 7, // Invalid day
        startTime: '09:00',
        endTime: '17:00'
      };

      await expect(TutorAvailabilityService.addAvailability(availabilityData))
        .rejects.toThrow('Invalid day of week. Use 0-6 (Sunday-Saturday)');
    });

    it('should throw error when start time is after end time', async () => {
      const availabilityData: CreateAvailabilityData = {
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '17:00',
        endTime: '09:00'
      };

      await expect(TutorAvailabilityService.addAvailability(availabilityData))
        .rejects.toThrow('Start time must be before end time');
    });

    it('should throw error for overlapping time slots', async () => {
      // Add first availability
      const firstAvailability: CreateAvailabilityData = {
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '09:00',
        endTime: '12:00'
      };

      await TutorAvailabilityService.addAvailability(firstAvailability);

      // Try to add overlapping availability
      const overlappingAvailability: CreateAvailabilityData = {
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '11:00',
        endTime: '15:00'
      };

      await expect(TutorAvailabilityService.addAvailability(overlappingAvailability))
        .rejects.toThrow('Time slot overlaps with existing availability');
    });

    it('should throw error if tutor does not exist', async () => {
      const availabilityData: CreateAvailabilityData = {
        tutorId: 'non-existent-tutor-id',
        dayOfWeek: 1,
        startTime: '09:00',
        endTime: '17:00'
      };

      await expect(TutorAvailabilityService.addAvailability(availabilityData))
        .rejects.toThrow('Tutor profile not found');
    });
  });

  describe('getTutorAvailability', () => {
    beforeEach(async () => {
      // Add some test availability slots
      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 1, // Monday
        startTime: '09:00',
        endTime: '12:00'
      });

      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 1, // Monday
        startTime: '14:00',
        endTime: '17:00'
      });

      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 2, // Tuesday
        startTime: '10:00',
        endTime: '16:00'
      });
    });

    it('should return all availability slots for a tutor', async () => {
      const availability = await TutorAvailabilityService.getTutorAvailability(testTutorId);

      expect(availability).toHaveLength(3);
      expect(availability[0].dayOfWeek).toBe(1);
      expect(availability[0].startTime).toBe('09:00');
      expect(availability[1].dayOfWeek).toBe(1);
      expect(availability[1].startTime).toBe('14:00');
      expect(availability[2].dayOfWeek).toBe(2);
    });

    it('should return empty array for tutor with no availability', async () => {
      // Create another tutor
      const anotherUser = await prisma.user.create({
        data: {
          email: `no-availability-${Date.now()}@example.com`,
          name: 'No Availability User',
          password: 'hashedpassword'
        }
      });

      const anotherTutor = await prisma.tutorProfile.create({
        data: {
          userId: anotherUser.id,
          title: 'Another Tutor',
          status: 'approved'
        }
      });

      const availability = await TutorAvailabilityService.getTutorAvailability(anotherTutor.id);
      expect(availability).toHaveLength(0);

      // Clean up
      await prisma.tutorProfile.delete({ where: { id: anotherTutor.id } });
      await prisma.user.delete({ where: { id: anotherUser.id } });
    });
  });

  describe('updateAvailability', () => {
    let availabilityId: string;

    beforeEach(async () => {
      const availability = await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '09:00',
        endTime: '17:00'
      });
      availabilityId = availability.id;
    });

    it('should update availability slot successfully', async () => {
      const updateData = {
        startTime: '10:00',
        endTime: '18:00'
      };

      const updatedAvailability = await TutorAvailabilityService.updateAvailability(availabilityId, updateData);

      expect(updatedAvailability.startTime).toBe('10:00');
      expect(updatedAvailability.endTime).toBe('18:00');
      expect(updatedAvailability.dayOfWeek).toBe(1); // Should remain unchanged
    });

    it('should throw error if availability slot does not exist', async () => {
      const updateData = {
        startTime: '10:00'
      };

      await expect(TutorAvailabilityService.updateAvailability('non-existent-id', updateData))
        .rejects.toThrow('Availability slot not found');
    });

    it('should throw error for invalid time format', async () => {
      const updateData = {
        startTime: '25:00'
      };

      await expect(TutorAvailabilityService.updateAvailability(availabilityId, updateData))
        .rejects.toThrow('Invalid start time format. Use HH:MM format');
    });
  });

  describe('deleteAvailability', () => {
    let availabilityId: string;

    beforeEach(async () => {
      const availability = await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '09:00',
        endTime: '17:00'
      });
      availabilityId = availability.id;
    });

    it('should delete availability slot successfully', async () => {
      await TutorAvailabilityService.deleteAvailability(availabilityId);

      const availability = await TutorAvailabilityService.getTutorAvailability(testTutorId);
      expect(availability).toHaveLength(0);
    });

    it('should throw error if availability slot does not exist', async () => {
      await expect(TutorAvailabilityService.deleteAvailability('non-existent-id'))
        .rejects.toThrow('Availability slot not found');
    });
  });

  describe('getAvailabilityByDay', () => {
    beforeEach(async () => {
      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '09:00',
        endTime: '12:00'
      });

      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 1,
        startTime: '14:00',
        endTime: '17:00'
      });

      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 2,
        startTime: '10:00',
        endTime: '16:00'
      });
    });

    it('should return availability for specific day', async () => {
      const mondayAvailability = await TutorAvailabilityService.getAvailabilityByDay(testTutorId, 1);
      expect(mondayAvailability).toHaveLength(2);
      expect(mondayAvailability[0].startTime).toBe('09:00');
      expect(mondayAvailability[1].startTime).toBe('14:00');

      const tuesdayAvailability = await TutorAvailabilityService.getAvailabilityByDay(testTutorId, 2);
      expect(tuesdayAvailability).toHaveLength(1);
      expect(tuesdayAvailability[0].startTime).toBe('10:00');
    });

    it('should throw error for invalid day of week', async () => {
      await expect(TutorAvailabilityService.getAvailabilityByDay(testTutorId, 7))
        .rejects.toThrow('Invalid day of week. Use 0-6 (Sunday-Saturday)');
    });
  });

  describe('getFormattedAvailability', () => {
    beforeEach(async () => {
      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 0, // Sunday
        startTime: '10:00',
        endTime: '14:00'
      });

      await TutorAvailabilityService.addAvailability({
        tutorId: testTutorId,
        dayOfWeek: 1, // Monday
        startTime: '09:00',
        endTime: '17:00'
      });
    });

    it('should return formatted availability by day name', async () => {
      const formatted = await TutorAvailabilityService.getFormattedAvailability(testTutorId);

      expect(formatted).toHaveProperty('Sunday');
      expect(formatted).toHaveProperty('Monday');
      expect(formatted.Sunday).toHaveLength(1);
      expect(formatted.Monday).toHaveLength(1);
      expect(formatted.Sunday[0].startTime).toBe('10:00');
      expect(formatted.Monday[0].startTime).toBe('09:00');
    });
  });
});

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { prisma } from '../../src/lib/prisma';
import { TutorEducationService, CreateEducationData } from '../../src/services/tutorEducationService';

describe('TutorEducationService', () => {
  let testUserId: string;
  let testTutorId: string;

  beforeEach(async () => {
    // Create a test user and tutor profile
    const testUser = await prisma.user.create({
      data: {
        email: `education-test-${Date.now()}@example.com`,
        name: 'Test Tutor User',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    testUserId = testUser.id;

    const tutorProfile = await prisma.tutorProfile.create({
      data: {
        userId: testUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 50,
        status: 'approved'
      }
    });
    testTutorId = tutorProfile.id;
  });

  afterEach(async () => {
    // Clean up test data in correct order
    try {
      await prisma.tutorEducation.deleteMany({
        where: { tutorId: testTutorId }
      });

      await prisma.tutorProfile.delete({
        where: { id: testTutorId }
      });

      await prisma.user.delete({
        where: { id: testUserId }
      });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('addEducation', () => {
    it('should add education record successfully', async () => {
      const educationData: CreateEducationData = {
        tutorId: testTutorId,
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        institution: 'MIT',
        startYear: 2010,
        endYear: 2014,
        description: 'Focused on algorithms and data structures'
      };

      const education = await TutorEducationService.addEducation(educationData);

      expect(education).toBeDefined();
      expect(education.tutorId).toBe(testTutorId);
      expect(education.degree).toBe('Bachelor of Science');
      expect(education.fieldOfStudy).toBe('Computer Science');
      expect(education.institution).toBe('MIT');
      expect(education.startYear).toBe(2010);
      expect(education.endYear).toBe(2014);
      expect(education.description).toBe('Focused on algorithms and data structures');
    });

    it('should add education record without end year', async () => {
      const educationData: CreateEducationData = {
        tutorId: testTutorId,
        degree: 'PhD',
        fieldOfStudy: 'Computer Science',
        institution: 'Stanford',
        startYear: 2020
      };

      const education = await TutorEducationService.addEducation(educationData);

      expect(education).toBeDefined();
      expect(education.endYear).toBeNull();
    });

    it('should throw error for invalid start year', async () => {
      const educationData: CreateEducationData = {
        tutorId: testTutorId,
        degree: 'Bachelor',
        fieldOfStudy: 'CS',
        institution: 'University',
        startYear: 1800 // Too early
      };

      await expect(TutorEducationService.addEducation(educationData))
        .rejects.toThrow('Invalid start year');
    });

    it('should throw error for invalid end year', async () => {
      const educationData: CreateEducationData = {
        tutorId: testTutorId,
        degree: 'Bachelor',
        fieldOfStudy: 'CS',
        institution: 'University',
        startYear: 2020,
        endYear: 2019 // Before start year
      };

      await expect(TutorEducationService.addEducation(educationData))
        .rejects.toThrow('Invalid end year');
    });

    it('should throw error if tutor does not exist', async () => {
      const educationData: CreateEducationData = {
        tutorId: 'non-existent-tutor-id',
        degree: 'Bachelor',
        fieldOfStudy: 'CS',
        institution: 'University',
        startYear: 2020
      };

      await expect(TutorEducationService.addEducation(educationData))
        .rejects.toThrow('Tutor profile not found');
    });
  });

  describe('getTutorEducation', () => {
    beforeEach(async () => {
      // Add some test education records
      await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        institution: 'MIT',
        startYear: 2010,
        endYear: 2014
      });

      await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Master of Science',
        fieldOfStudy: 'Computer Science',
        institution: 'Stanford',
        startYear: 2015,
        endYear: 2017
      });
    });

    it('should return all education records for a tutor', async () => {
      const education = await TutorEducationService.getTutorEducation(testTutorId);

      expect(education).toHaveLength(2);
      // Should be ordered by start year descending
      expect(education[0].startYear).toBe(2015); // Master's first
      expect(education[1].startYear).toBe(2010); // Bachelor's second
    });

    it('should return empty array for tutor with no education', async () => {
      // Create another tutor
      const anotherUser = await prisma.user.create({
        data: {
          email: `no-education-${Date.now()}@example.com`,
          name: 'No Education User',
          password: 'hashedpassword'
        }
      });

      const anotherTutor = await prisma.tutorProfile.create({
        data: {
          userId: anotherUser.id,
          title: 'Another Tutor',
          status: 'approved'
        }
      });

      const education = await TutorEducationService.getTutorEducation(anotherTutor.id);
      expect(education).toHaveLength(0);

      // Clean up
      await prisma.tutorProfile.delete({ where: { id: anotherTutor.id } });
      await prisma.user.delete({ where: { id: anotherUser.id } });
    });
  });

  describe('updateEducation', () => {
    let educationId: string;

    beforeEach(async () => {
      const education = await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Bachelor',
        fieldOfStudy: 'CS',
        institution: 'University',
        startYear: 2010,
        endYear: 2014
      });
      educationId = education.id;
    });

    it('should update education record successfully', async () => {
      const updateData = {
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        institution: 'MIT'
      };

      const updatedEducation = await TutorEducationService.updateEducation(educationId, updateData);

      expect(updatedEducation.degree).toBe('Bachelor of Science');
      expect(updatedEducation.fieldOfStudy).toBe('Computer Science');
      expect(updatedEducation.institution).toBe('MIT');
      expect(updatedEducation.startYear).toBe(2010); // Should remain unchanged
    });

    it('should throw error if education record does not exist', async () => {
      const updateData = {
        degree: 'Master'
      };

      await expect(TutorEducationService.updateEducation('non-existent-id', updateData))
        .rejects.toThrow('Education record not found');
    });

    it('should throw error for invalid year update', async () => {
      const updateData = {
        startYear: 1800
      };

      await expect(TutorEducationService.updateEducation(educationId, updateData))
        .rejects.toThrow('Invalid start year');
    });
  });

  describe('deleteEducation', () => {
    let educationId: string;

    beforeEach(async () => {
      const education = await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Bachelor',
        fieldOfStudy: 'CS',
        institution: 'University',
        startYear: 2010
      });
      educationId = education.id;
    });

    it('should delete education record successfully', async () => {
      await TutorEducationService.deleteEducation(educationId);

      const education = await TutorEducationService.getTutorEducation(testTutorId);
      expect(education).toHaveLength(0);
    });

    it('should throw error if education record does not exist', async () => {
      await expect(TutorEducationService.deleteEducation('non-existent-id'))
        .rejects.toThrow('Education record not found');
    });
  });

  describe('getEducationByDegree', () => {
    beforeEach(async () => {
      await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        institution: 'MIT',
        startYear: 2010
      });

      await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Master of Science',
        fieldOfStudy: 'Data Science',
        institution: 'Stanford',
        startYear: 2015
      });
    });

    it('should return education records matching degree', async () => {
      const bachelorEducation = await TutorEducationService.getEducationByDegree(testTutorId, 'bachelor');
      expect(bachelorEducation).toHaveLength(1);
      expect(bachelorEducation[0].degree).toBe('Bachelor of Science');

      const masterEducation = await TutorEducationService.getEducationByDegree(testTutorId, 'master');
      expect(masterEducation).toHaveLength(1);
      expect(masterEducation[0].degree).toBe('Master of Science');
    });
  });

  describe('getHighestEducation', () => {
    beforeEach(async () => {
      await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        institution: 'MIT',
        startYear: 2010
      });

      await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'PhD in Computer Science',
        fieldOfStudy: 'Machine Learning',
        institution: 'Stanford',
        startYear: 2018
      });

      await TutorEducationService.addEducation({
        tutorId: testTutorId,
        degree: 'Master of Science',
        fieldOfStudy: 'Data Science',
        institution: 'Berkeley',
        startYear: 2015
      });
    });

    it('should return highest education level', async () => {
      const highest = await TutorEducationService.getHighestEducation(testTutorId);

      expect(highest).toBeDefined();
      expect(highest!.degree).toBe('PhD in Computer Science');
    });

    it('should return null for tutor with no education', async () => {
      // Create another tutor
      const anotherUser = await prisma.user.create({
        data: {
          email: `no-education-highest-${Date.now()}@example.com`,
          name: 'No Education User',
          password: 'hashedpassword'
        }
      });

      const anotherTutor = await prisma.tutorProfile.create({
        data: {
          userId: anotherUser.id,
          title: 'Another Tutor',
          status: 'approved'
        }
      });

      const highest = await TutorEducationService.getHighestEducation(anotherTutor.id);
      expect(highest).toBeNull();

      // Clean up
      await prisma.tutorProfile.delete({ where: { id: anotherTutor.id } });
      await prisma.user.delete({ where: { id: anotherUser.id } });
    });
  });

  describe('validateEducationData', () => {
    it('should return no errors for valid data', async () => {
      const validData: CreateEducationData = {
        tutorId: testTutorId,
        degree: 'Bachelor of Science',
        fieldOfStudy: 'Computer Science',
        institution: 'MIT',
        startYear: 2010,
        endYear: 2014
      };

      const errors = TutorEducationService.validateEducationData(validData);
      expect(errors).toHaveLength(0);
    });

    it('should return errors for invalid data', async () => {
      const invalidData: CreateEducationData = {
        tutorId: testTutorId,
        degree: 'A', // Too short
        fieldOfStudy: 'B', // Too short
        institution: 'C', // Too short
        startYear: 1800, // Too early
        endYear: 2050 // Too far in future
      };

      const errors = TutorEducationService.validateEducationData(invalidData);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.includes('Degree must be at least 2 characters'))).toBe(true);
      expect(errors.some(error => error.includes('Field of study must be at least 2 characters'))).toBe(true);
      expect(errors.some(error => error.includes('Institution must be at least 2 characters'))).toBe(true);
    });
  });
});

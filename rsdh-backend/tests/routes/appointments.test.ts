import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { createApp } from '../../src/app';
import { FastifyInstance } from 'fastify';
import { prisma } from '../../src/lib/prisma';
import { generateSimpleToken } from '../../src/middleware/auth';

describe('Appointment Routes', () => {
  let app: FastifyInstance;
  let studentToken: string;
  let tutorToken: string;
  let adminToken: string;
  let studentId: string;
  let tutorUserId: string;
  let tutorId: string;
  let appointmentId: string;

  beforeAll(async () => {
    app = await createApp();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Create test student
    const student = await prisma.user.create({
      data: {
        email: `student-${Date.now()}@example.com`,
        name: 'Test Student',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    studentId = student.id;
    studentToken = generateSimpleToken(student.email);

    // Create test tutor user
    const tutorUser = await prisma.user.create({
      data: {
        email: `tutor-${Date.now()}@example.com`,
        name: 'Test Tutor',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    tutorUserId = tutorUser.id;
    tutorToken = generateSimpleToken(tutorUser.email);

    // Create tutor profile
    const tutorProfile = await prisma.tutorProfile.create({
      data: {
        userId: tutorUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 100,
        status: 'approved'
      }
    });
    tutorId = tutorProfile.id;

    // Add availability for the tutor (Monday 9-17)
    await prisma.tutorAvailability.create({
      data: {
        tutorId: tutorId,
        dayOfWeek: 1, // Monday
        startTime: '09:00',
        endTime: '17:00'
      }
    });

    // Create admin user
    const admin = await prisma.user.create({
      data: {
        email: `admin-${Date.now()}@example.com`,
        name: 'Test Admin',
        password: 'hashedpassword',
        role: 'admin'
      }
    });
    adminToken = generateSimpleToken(admin.email);
  });

  afterEach(async () => {
    // Clean up test data in correct order
    try {
      await prisma.appointment.deleteMany({
        where: {
          OR: [
            { tutorId: tutorId },
            { studentId: studentId }
          ]
        }
      });

      await prisma.tutorAvailability.deleteMany({
        where: { tutorId: tutorId }
      });

      await prisma.tutorProfile.delete({
        where: { id: tutorId }
      });

      await prisma.user.deleteMany({
        where: {
          email: {
            contains: Date.now().toString().substring(0, 8) // Rough cleanup
          }
        }
      });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('POST /api/appointments', () => {
    it('should create appointment successfully', async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const response = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          tutorId: tutorId,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789',
          notes: 'Test appointment'
        }
      });

      expect(response.statusCode).toBe(201);
      const appointment = JSON.parse(response.body);
      expect(appointment.tutorId).toBe(tutorId);
      expect(appointment.studentId).toBe(studentId);
      expect(appointment.meetingType).toBe('online');
      expect(appointment.status).toBe('scheduled');
      appointmentId = appointment.id;
    });

    it('should return 400 for invalid tutor', async () => {
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const response = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          tutorId: 'invalid-tutor-id',
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789'
        }
      });

      expect(response.statusCode).toBe(400);
      const error = JSON.parse(response.body);
      expect(error.message).toContain('Tutor not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        payload: {
          tutorId: tutorId,
          startTime: new Date().toISOString(),
          endTime: new Date().toISOString(),
          meetingType: 'online'
        }
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('GET /api/appointments/:appointmentId', () => {
    beforeEach(async () => {
      // Create a test appointment
      const nextMonday = getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const appointment = await prisma.appointment.create({
        data: {
          tutorId: tutorId,
          studentId: studentId,
          startTime,
          endTime,
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789',
          status: 'scheduled'
        }
      });
      appointmentId = appointment.id;
    });

    it('should return appointment details for student', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/appointments/${appointmentId}`,
        headers: {
          authorization: `Bearer ${studentToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const appointment = JSON.parse(response.body);
      expect(appointment.id).toBe(appointmentId);
      expect(appointment.tutor).toBeDefined();
      expect(appointment.student).toBeDefined();
      expect(appointment.tutor.user.name).toBe('Test Tutor');
      expect(appointment.student.name).toBe('Test Student');
    });

    it('should return appointment details for tutor', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/appointments/${appointmentId}`,
        headers: {
          authorization: `Bearer ${tutorToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const appointment = JSON.parse(response.body);
      expect(appointment.id).toBe(appointmentId);
    });

    it('should return 404 for non-existent appointment', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/appointments/non-existent-id',
        headers: {
          authorization: `Bearer ${studentToken}`
        }
      });

      expect(response.statusCode).toBe(404);
    });

    it('should return 401 without authentication', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/appointments/${appointmentId}`
      });

      expect(response.statusCode).toBe(401);
    });
  });
});

// Helper function
function getNextMonday(): Date {
  const today = new Date();
  const nextMonday = new Date(today);
  nextMonday.setDate(today.getDate() + (1 + 7 - today.getDay()) % 7);
  if (nextMonday <= today) {
    nextMonday.setDate(nextMonday.getDate() + 7);
  }
  return nextMonday;
}

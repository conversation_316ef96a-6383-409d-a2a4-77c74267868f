import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { createApp } from '../../src/app';
import { FastifyInstance } from 'fastify';
import { prisma } from '../../src/lib/prisma';
import { generateSimpleToken } from '../../src/middleware/auth';

describe('Review Routes', () => {
  let app: FastifyInstance;
  let studentToken: string;
  let tutorToken: string;
  let adminToken: string;
  let studentId: string;
  let tutorUserId: string;
  let tutorId: string;
  let appointmentId: string;
  let reviewId: string;

  beforeAll(async () => {
    app = await createApp();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    // Create test student
    const student = await prisma.user.create({
      data: {
        email: `student-${Date.now()}@example.com`,
        name: 'Test Student',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    studentId = student.id;
    studentToken = generateSimpleToken(student.email);

    // Create test tutor user
    const tutorUser = await prisma.user.create({
      data: {
        email: `tutor-${Date.now()}@example.com`,
        name: 'Test Tutor',
        password: 'hashedpassword',
        role: 'user'
      }
    });
    tutorUserId = tutorUser.id;
    tutorToken = generateSimpleToken(tutorUser.email);

    // Create tutor profile
    const tutorProfile = await prisma.tutorProfile.create({
      data: {
        userId: tutorUserId,
        title: 'Test Tutor',
        bio: 'Test bio',
        rate: 100,
        status: 'approved'
      }
    });
    tutorId = tutorProfile.id;

    // Create completed appointment
    const appointment = await prisma.appointment.create({
      data: {
        tutorId: tutorId,
        studentId: studentId,
        startTime: new Date('2023-01-01T10:00:00Z'),
        endTime: new Date('2023-01-01T11:00:00Z'),
        meetingType: 'online',
        meetingLink: 'https://zoom.us/j/123456789',
        status: 'completed'
      }
    });
    appointmentId = appointment.id;

    // Create admin user
    const admin = await prisma.user.create({
      data: {
        email: `admin-${Date.now()}@example.com`,
        name: 'Test Admin',
        password: 'hashedpassword',
        role: 'admin'
      }
    });
    adminToken = generateSimpleToken(admin.email);
  });

  afterEach(async () => {
    // Clean up test data in correct order
    try {
      await prisma.review.deleteMany({
        where: { appointmentId: appointmentId }
      });

      await prisma.appointment.deleteMany({
        where: {
          OR: [
            { tutorId: tutorId },
            { studentId: studentId }
          ]
        }
      });

      await prisma.tutorProfile.delete({
        where: { id: tutorId }
      });

      await prisma.user.deleteMany({
        where: {
          email: {
            contains: Date.now().toString().substring(0, 8) // Rough cleanup
          }
        }
      });
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('POST /api/reviews', () => {
    it('should create review successfully', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          appointmentId: appointmentId,
          tutorId: tutorId,
          rating: 5,
          comment: 'Excellent tutor!'
        }
      });

      expect(response.statusCode).toBe(201);
      const review = JSON.parse(response.body);
      expect(review.appointmentId).toBe(appointmentId);
      expect(review.tutorId).toBe(tutorId);
      expect(review.rating).toBe(5);
      expect(review.comment).toBe('Excellent tutor!');
      reviewId = review.id;
    });

    it('should return 400 for invalid rating', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          appointmentId: appointmentId,
          tutorId: tutorId,
          rating: 6, // Invalid rating
          comment: 'Test comment'
        }
      });

      expect(response.statusCode).toBe(400);
      const error = JSON.parse(response.body);
      expect(error.message).toContain('must be <= 5');
    });

    it('should return 400 for non-existent appointment', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          appointmentId: 'non-existent-appointment',
          tutorId: tutorId,
          rating: 5,
          comment: 'Test comment'
        }
      });

      expect(response.statusCode).toBe(400);
      const error = JSON.parse(response.body);
      expect(error.message).toContain('Appointment not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        payload: {
          appointmentId: appointmentId,
          tutorId: tutorId,
          rating: 5
        }
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('GET /api/reviews/:reviewId', () => {
    beforeEach(async () => {
      // Create a test review
      const review = await prisma.review.create({
        data: {
          appointmentId: appointmentId,
          tutorId: tutorId,
          rating: 5,
          comment: 'Test review'
        }
      });
      reviewId = review.id;
    });

    it('should return review details', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/reviews/${reviewId}`,
        headers: {
          authorization: `Bearer ${studentToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const review = JSON.parse(response.body);
      expect(review.id).toBe(reviewId);
      expect(review.rating).toBe(5);
      expect(review.comment).toBe('Test review');
      expect(review.appointment).toBeDefined();
      expect(review.tutor).toBeDefined();
    });

    it('should return 404 for non-existent review', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/reviews/non-existent-id',
        headers: {
          authorization: `Bearer ${studentToken}`
        }
      });

      expect(response.statusCode).toBe(404);
    });

    it('should return 401 without authentication', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/reviews/${reviewId}`
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('PUT /api/reviews/:reviewId', () => {
    beforeEach(async () => {
      // Create a test review
      const review = await prisma.review.create({
        data: {
          appointmentId: appointmentId,
          tutorId: tutorId,
          rating: 4,
          comment: 'Original comment'
        }
      });
      reviewId = review.id;
    });

    it('should update review successfully', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: `/api/reviews/${reviewId}`,
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          rating: 5,
          comment: 'Updated comment'
        }
      });

      expect(response.statusCode).toBe(200);
      const review = JSON.parse(response.body);
      expect(review.rating).toBe(5);
      expect(review.comment).toBe('Updated comment');
    });

    it('should return 400 for invalid rating', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: `/api/reviews/${reviewId}`,
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          rating: 0 // Invalid rating
        }
      });

      expect(response.statusCode).toBe(400);
    });

    it('should return 404 for non-existent review', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: '/api/reviews/non-existent-id',
        headers: {
          authorization: `Bearer ${studentToken}`
        },
        payload: {
          rating: 5
        }
      });

      expect(response.statusCode).toBe(404);
    });

    it('should return 401 without authentication', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: `/api/reviews/${reviewId}`,
        payload: {
          rating: 5
        }
      });

      expect(response.statusCode).toBe(401);
    });
  });

  describe('GET /api/reviews/tutor/:tutorId', () => {
    beforeEach(async () => {
      // Create multiple reviews for the tutor
      await prisma.review.create({
        data: {
          appointmentId: appointmentId,
          tutorId: tutorId,
          rating: 5,
          comment: 'Great tutor!'
        }
      });
    });

    it('should return tutor reviews', async () => {
      const response = await app.inject({
        method: 'GET',
        url: `/api/reviews/tutor/${tutorId}`,
        headers: {
          authorization: `Bearer ${studentToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.reviews).toBeDefined();
      expect(result.total).toBeGreaterThan(0);
      expect(result.reviews.length).toBeGreaterThan(0);
      expect(result.reviews[0].rating).toBe(5);
    });

    it('should return empty array for tutor with no reviews', async () => {
      // Create another tutor without reviews
      const anotherTutorUser = await prisma.user.create({
        data: {
          email: `another-tutor-${Date.now()}@example.com`,
          name: 'Another Tutor',
          password: 'hashedpassword',
          role: 'user'
        }
      });

      const anotherTutorProfile = await prisma.tutorProfile.create({
        data: {
          userId: anotherTutorUser.id,
          title: 'Another Tutor',
          status: 'approved'
        }
      });

      const response = await app.inject({
        method: 'GET',
        url: `/api/reviews/tutor/${anotherTutorProfile.id}`,
        headers: {
          authorization: `Bearer ${studentToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      const result = JSON.parse(response.body);
      expect(result.reviews).toEqual([]);
      expect(result.total).toBe(0);

      // Cleanup
      await prisma.tutorProfile.delete({ where: { id: anotherTutorProfile.id } });
      await prisma.user.delete({ where: { id: anotherTutorUser.id } });
    });
  });
});

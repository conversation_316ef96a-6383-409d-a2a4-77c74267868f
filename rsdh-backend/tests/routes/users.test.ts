import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FastifyInstance } from 'fastify';
import { createApp } from '../../src/app';
import { UserService } from '../../src/services/userService';
import { testPrisma } from '../setup';
import { createTestAdmin, createTestRegularUser, createTestUser, getAuthHeaders, TestUser } from '../helpers/auth';

describe('User Routes', () => {
  let app: FastifyInstance;
  let adminUser: TestUser;
  let regularUser: TestUser;

  beforeEach(async () => {
    // Clean up database first
    await testPrisma.user.deleteMany();

    // Set unique test environment variables for each test
    const timestamp = Date.now();
    process.env.ADMIN_EMAIL = `admin-${timestamp}@example.com`;
    process.env.ADMIN_PASSWORD = 'testpassword123';

    // Create a fresh app instance for each test
    app = await createApp();
    await app.ready();

    // Create test users with unique emails to avoid conflicts
    adminUser = await createTestUser(app, {
      email: `admin-${timestamp}@example.com`,
      name: 'Test Admin',
      password: 'testpassword123',
      role: 'admin',
    });

    regularUser = await createTestUser(app, {
      email: `user-${timestamp}@example.com`,
      name: 'Test User',
      password: 'testpassword123',
      role: 'user',
    });
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /api/users/:id', () => {
    it('should get user by ID', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      const response = await app.inject({
        method: 'GET',
        url: `/api/users/${user.id}`,
        headers: getAuthHeaders(adminUser.token),
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        id: user.id,
        email: user.email,
        name: user.name,
      });
    });

    it('should return 404 for non-existent user', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users/non-existent-id',
        headers: getAuthHeaders(adminUser.token),
      });

      expect(response.statusCode).toBe(404);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        error: 'Not Found',
        message: 'User not found',
      });
    });
  });

  describe('GET /api/users', () => {
    it('should get all users with pagination', async () => {
      // Create additional test users (admin and regular users already exist from beforeEach)
      await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'User 1',
      });
      await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'User 2',
      });
      await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'User 3',
      });

      const response = await app.inject({
        method: 'GET',
        url: '/api/users?page=1&limit=2',
        headers: getAuthHeaders(adminUser.token),
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.users).toHaveLength(2);
      expect(responseBody.total).toBe(5); // admin + regular + 3 test users
      expect(responseBody.page).toBe(1);
      expect(responseBody.limit).toBe(2);
    });

    it('should return only admin and regular users when no additional users exist', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users',
        headers: getAuthHeaders(adminUser.token),
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.users).toHaveLength(2); // admin + regular user
      expect(responseBody.total).toBe(2);
    });
  });

  describe('PATCH /api/users/:id/status', () => {
    it('should toggle user status', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/status`,
        headers: getAuthHeaders(adminUser.token),
        payload: {
          disabled: true,
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.disabled).toBe(true);

      // Verify in database
      const updatedUser = await UserService.findById(user.id);
      expect(updatedUser?.disabled).toBe(true);
    });

    it('should enable disabled user', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      // First disable the user
      await UserService.toggleUserStatus(user.id, true);

      // Then enable the user
      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/status`,
        headers: getAuthHeaders(adminUser.token),
        payload: {
          disabled: false,
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.disabled).toBe(false);
    });
  });

  describe('PATCH /api/users/:id/role', () => {
    it('should update user role to admin', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
      });

      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/role`,
        headers: getAuthHeaders(adminUser.token),
        payload: {
          role: 'admin',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.role).toBe('admin');

      // Verify in database
      const updatedUser = await UserService.findById(user.id);
      expect(updatedUser?.role).toBe('admin');
    });

    it('should update admin role to user', async () => {
      const user = await UserService.createUser({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Admin User',
        role: 'admin',
      });

      const response = await app.inject({
        method: 'PATCH',
        url: `/api/users/${user.id}/role`,
        headers: getAuthHeaders(adminUser.token),
        payload: {
          role: 'user',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody.role).toBe('user');
    });
  });

  describe('GET /api/users/profile', () => {
    it('should return current user profile', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/users/profile',
        headers: getAuthHeaders(adminUser.token),
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        id: adminUser.id,
        email: adminUser.email,
        name: adminUser.name,
        role: adminUser.role,
        disabled: false,
      });
      expect(responseBody).toHaveProperty('createdAt');
      expect(responseBody).toHaveProperty('updatedAt');
    });
  });

  describe('PUT /api/users/profile', () => {
    it('should update current user profile', async () => {
      const response = await app.inject({
        method: 'PUT',
        url: '/api/users/profile',
        headers: getAuthHeaders(adminUser.token),
        payload: {
          name: 'Updated Admin Name',
          avatar: 'https://example.com/new-avatar.jpg',
        },
      });

      expect(response.statusCode).toBe(200);
      const responseBody = JSON.parse(response.body);
      expect(responseBody).toMatchObject({
        id: adminUser.id,
        email: adminUser.email,
        name: 'Updated Admin Name',
        avatar: 'https://example.com/new-avatar.jpg',
        role: adminUser.role,
        disabled: false,
      });
      expect(responseBody).toHaveProperty('createdAt');
      expect(responseBody).toHaveProperty('updatedAt');
    });
  });
});

import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { FastifyInstance } from 'fastify';
import { createApp } from '../../src/app';
import { TestUtils } from '../helpers/testUtils';
import { generateSimpleToken } from '../../src/middleware/auth';
import { prisma } from '../../src/lib/prisma';

describe('Comprehensive API Integration Tests', () => {
  let app: FastifyInstance;
  let adminToken: string;
  let userToken: string;
  let tutorToken: string;

  beforeAll(async () => {
    app = await createApp();
    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  beforeEach(async () => {
    await TestUtils.cleanDatabase();

    // Create admin user directly in database
    const adminUser = await TestUtils.createTestUser(
      TestUtils.generateUniqueEmail('admin'),
      'Admin User'
    );

    // Update to admin role
    await prisma.user.update({
      where: { id: adminUser.id },
      data: { role: 'admin' } as any
    });

    adminToken = generateSimpleToken(adminUser.email);

    // Create regular user directly in database
    const regularUser = await TestUtils.createTestUser(
      TestUtils.generateUniqueEmail('user'),
      'Regular User'
    );
    userToken = generateSimpleToken(regularUser.email);

    // Create tutor user directly in database
    const tutorUser = await TestUtils.createTestUser(
      TestUtils.generateUniqueEmail('tutor'),
      'Tutor User'
    );
    tutorToken = generateSimpleToken(tutorUser.email);
  });

  afterEach(async () => {
    await TestUtils.cleanDatabase();
  });

  describe('Complete Tutoring Platform Flow', () => {
    it('should handle end-to-end tutoring workflow', async () => {
      // 1. Apply to become tutor
      const applicationResponse = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${tutorToken}`
        },
        payload: {
          title: 'Math Tutor',
          bio: 'Experienced math teacher with 10 years of experience',
          rate: 50,
          education: [{
            institution: 'MIT',
            degree: 'PhD',
            fieldOfStudy: 'Mathematics',
            startYear: 2010,
            endYear: 2014
          }],
          career: [{
            title: 'Professor',
            company: 'University',
            startYear: 2015,
            current: true
          }]
        }
      });

      expect(applicationResponse.statusCode).toBe(201);
      const application = JSON.parse(applicationResponse.body);
      const tutorId = application.id;
      expect(application.title).toBe('Math Tutor');
      expect(application.status).toBe('pending');

      // 2. Admin reviews and approves tutor
      const approvalResponse = await app.inject({
        method: 'PATCH',
        url: `/api/tutors/manage/${tutorId}/status`,
        headers: {
          authorization: `Bearer ${adminToken}`
        },
        payload: {
          status: 'approved'
        }
      });

      expect(approvalResponse.statusCode).toBe(200);
      const approvedTutor = JSON.parse(approvalResponse.body);
      expect(approvedTutor.status).toBe('approved');

      // 3. Tutor adds availability
      const availabilityResponse = await app.inject({
        method: 'POST',
        url: '/api/tutors/manage/availability',
        headers: {
          authorization: `Bearer ${tutorToken}`
        },
        payload: {
          dayOfWeek: 1, // Monday
          startTime: '09:00',
          endTime: '17:00'
        }
      });

      expect(availabilityResponse.statusCode).toBe(201);

      // 4. Student searches for tutors
      const searchResponse = await app.inject({
        method: 'GET',
        url: '/api/tutors?status=approved',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(searchResponse.statusCode).toBe(200);
      const searchResults = JSON.parse(searchResponse.body);
      expect(searchResults.tutors).toHaveLength(1);
      expect(searchResults.tutors[0].id).toBe(tutorId);

      // 5. Student checks available time slots
      const nextMonday = TestUtils.getNextMonday();
      const dateStr = nextMonday.toISOString().split('T')[0];

      const slotsResponse = await app.inject({
        method: 'GET',
        url: `/api/appointments/tutor/${tutorId}/available-slots?date=${dateStr}`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(slotsResponse.statusCode).toBe(200);
      const slots = JSON.parse(slotsResponse.body);
      expect(slots.length).toBeGreaterThan(0);

      // 6. Student books appointment
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const bookingResponse = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          tutorId,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789',
          notes: 'Need help with calculus'
        }
      });

      expect(bookingResponse.statusCode).toBe(201);
      const appointment = JSON.parse(bookingResponse.body);
      const appointmentId = appointment.id;
      expect(appointment.status).toBe('scheduled');

      // 7. Verify appointment appears in both student and tutor lists
      const studentAppointmentsResponse = await app.inject({
        method: 'GET',
        url: '/api/appointments/student/my-appointments',
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(studentAppointmentsResponse.statusCode).toBe(200);
      const studentAppointments = JSON.parse(studentAppointmentsResponse.body);
      expect(studentAppointments.appointments).toHaveLength(1);

      const tutorAppointmentsResponse = await app.inject({
        method: 'GET',
        url: '/api/appointments/tutor/my-appointments',
        headers: {
          authorization: `Bearer ${tutorToken}`
        }
      });

      expect(tutorAppointmentsResponse.statusCode).toBe(200);
      const tutorAppointments = JSON.parse(tutorAppointmentsResponse.body);
      expect(tutorAppointments.appointments).toHaveLength(1);

      // 8. Tutor completes the appointment
      const completeResponse = await app.inject({
        method: 'PATCH',
        url: `/api/appointments/${appointmentId}/complete`,
        headers: {
          authorization: `Bearer ${tutorToken}`
        }
      });

      expect(completeResponse.statusCode).toBe(200);
      const completedAppointment = JSON.parse(completeResponse.body);
      expect(completedAppointment.status).toBe('completed');

      // 9. Student creates a review
      const reviewResponse = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          appointmentId,
          tutorId,
          rating: 5,
          comment: 'Excellent tutoring session! Very helpful with calculus concepts.'
        }
      });

      expect(reviewResponse.statusCode).toBe(201);
      const review = JSON.parse(reviewResponse.body);
      expect(review.rating).toBe(5);
      expect(review.comment).toBe('Excellent tutoring session! Very helpful with calculus concepts.');

      // 10. Verify review appears in tutor's profile
      const tutorReviewsResponse = await app.inject({
        method: 'GET',
        url: `/api/reviews/tutor/${tutorId}`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(tutorReviewsResponse.statusCode).toBe(200);
      const tutorReviews = JSON.parse(tutorReviewsResponse.body);
      expect(tutorReviews.reviews).toHaveLength(1);
      expect(tutorReviews.reviews[0].rating).toBe(5);

      // 11. Check tutor review statistics
      const statsResponse = await app.inject({
        method: 'GET',
        url: `/api/reviews/tutor/${tutorId}/stats`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(statsResponse.statusCode).toBe(200);
      const stats = JSON.parse(statsResponse.body);
      expect(stats.totalReviews).toBe(1);
      expect(stats.averageRating).toBe(5);
      expect(stats.ratingDistribution[5]).toBe(1);

      // 12. Admin checks overall statistics
      const adminStatsResponse = await app.inject({
        method: 'GET',
        url: '/api/reviews/admin/statistics',
        headers: {
          authorization: `Bearer ${adminToken}`
        }
      });

      expect(adminStatsResponse.statusCode).toBe(200);
      const adminStats = JSON.parse(adminStatsResponse.body);
      expect(adminStats.totalReviews).toBe(1);
      expect(adminStats.averageRating).toBe(5);

      // 13. Verify updated tutor profile includes review data
      const finalTutorResponse = await app.inject({
        method: 'GET',
        url: `/api/tutors/${tutorId}`,
        headers: {
          authorization: `Bearer ${userToken}`
        }
      });

      expect(finalTutorResponse.statusCode).toBe(200);
      const finalTutor = JSON.parse(finalTutorResponse.body);
      expect(finalTutor.status).toBe('approved');
      expect(finalTutor.education).toHaveLength(1);
      expect(finalTutor.career).toHaveLength(1);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    let tutorId: string;

    beforeEach(async () => {
      // Create approved tutor for testing
      const applicationResponse = await app.inject({
        method: 'POST',
        url: '/api/tutors/apply',
        headers: {
          authorization: `Bearer ${tutorToken}`
        },
        payload: {
          title: 'Test Tutor',
          bio: 'Test bio',
          rate: 50,
          education: [{
            institution: 'Test University',
            degree: 'Bachelor',
            fieldOfStudy: 'Computer Science',
            startYear: 2018,
            endYear: 2022
          }]
        }
      });

      const application = JSON.parse(applicationResponse.body);
      tutorId = application.id;

      await app.inject({
        method: 'PATCH',
        url: `/api/tutors/manage/${tutorId}/status`,
        headers: {
          authorization: `Bearer ${adminToken}`
        },
        payload: { status: 'approved' }
      });

      await app.inject({
        method: 'POST',
        url: '/api/tutors/manage/availability',
        headers: {
          authorization: `Bearer ${tutorToken}`
        },
        payload: {
          dayOfWeek: 1,
          startTime: '09:00',
          endTime: '17:00'
        }
      });
    });

    it('should handle appointment booking conflicts', async () => {
      const nextMonday = TestUtils.getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      // Book first appointment
      const firstBookingResponse = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          tutorId,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789'
        }
      });

      expect(firstBookingResponse.statusCode).toBe(201);

      // Try to book conflicting appointment
      const conflictingBookingResponse = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          tutorId,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/987654321'
        }
      });

      expect(conflictingBookingResponse.statusCode).toBe(400);
      const error = JSON.parse(conflictingBookingResponse.body);
      expect(error.message).toBe('Tutor is not available at the requested time');
    });

    it('should prevent review of non-completed appointments', async () => {
      // Create scheduled appointment
      const nextMonday = TestUtils.getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const bookingResponse = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          tutorId,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789'
        }
      });

      const appointment = JSON.parse(bookingResponse.body);

      // Try to review scheduled appointment
      const reviewResponse = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          appointmentId: appointment.id,
          tutorId,
          rating: 5,
          comment: 'Great session!'
        }
      });

      expect(reviewResponse.statusCode).toBe(400);
      const error = JSON.parse(reviewResponse.body);
      expect(error.message).toBe('Can only review completed appointments');
    });

    it('should prevent duplicate reviews', async () => {
      // Create and complete appointment
      const nextMonday = TestUtils.getNextMonday();
      const startTime = new Date(nextMonday);
      startTime.setHours(10, 0, 0, 0);
      const endTime = new Date(nextMonday);
      endTime.setHours(11, 0, 0, 0);

      const bookingResponse = await app.inject({
        method: 'POST',
        url: '/api/appointments',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          tutorId,
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          meetingType: 'online',
          meetingLink: 'https://zoom.us/j/123456789'
        }
      });

      const appointment = JSON.parse(bookingResponse.body);

      await app.inject({
        method: 'PATCH',
        url: `/api/appointments/${appointment.id}/complete`,
        headers: {
          authorization: `Bearer ${tutorToken}`
        }
      });

      // Create first review
      const firstReviewResponse = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          appointmentId: appointment.id,
          tutorId,
          rating: 5,
          comment: 'Great session!'
        }
      });

      expect(firstReviewResponse.statusCode).toBe(201);

      // Try to create duplicate review
      const duplicateReviewResponse = await app.inject({
        method: 'POST',
        url: '/api/reviews',
        headers: {
          authorization: `Bearer ${userToken}`
        },
        payload: {
          appointmentId: appointment.id,
          tutorId,
          rating: 4,
          comment: 'Another review'
        }
      });

      expect(duplicateReviewResponse.statusCode).toBe(400);
      const error = JSON.parse(duplicateReviewResponse.body);
      expect(error.message).toBe('Review already exists for this appointment');
    });
  });
});

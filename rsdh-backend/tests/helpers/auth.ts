import { FastifyInstance } from 'fastify';
import { UserService } from '../../src/services/userService';
import { generateSimpleToken } from '../../src/middleware/auth';

export interface TestUser {
  id: string;
  email: string;
  name: string;
  role: string;
  token: string;
}

/**
 * Create a test user and return authentication token
 */
export async function createTestUser(
  app: FastifyInstance,
  userData: {
    email: string;
    name: string;
    password: string;
    role?: string;
  }
): Promise<TestUser> {
  // Create user using UserService
  const user = await UserService.createUser({
    email: userData.email,
    name: userData.name,
    password: userData.password,
    role: userData.role || 'user',
  });

  // Generate token for authentication
  const token = generateSimpleToken(user.email);

  return {
    id: user.id,
    email: user.email,
    name: user.name || '',
    role: user.role,
    token,
  };
}

/**
 * Create an admin test user
 */
export async function createTestAdmin(app: FastifyInstance): Promise<TestUser> {
  return createTestUser(app, {
    email: '<EMAIL>',
    name: 'Test Admin',
    password: 'adminpassword123',
    role: 'admin',
  });
}

/**
 * Create a regular test user
 */
export async function createTestRegularUser(app: FastifyInstance): Promise<TestUser> {
  return createTestUser(app, {
    email: '<EMAIL>',
    name: 'Test User',
    password: 'userpassword123',
    role: 'user',
  });
}

/**
 * Get authorization headers for a test user
 */
export function getAuthHeaders(token: string) {
  return {
    authorization: `Bearer ${token}`,
  };
}

/**
 * Login a user and get token (for testing auth routes)
 */
export async function loginTestUser(
  app: FastifyInstance,
  email: string,
  password: string
): Promise<string> {
  const response = await app.inject({
    method: 'POST',
    url: '/api/auth/login',
    payload: {
      email,
      password,
    },
  });

  if (response.statusCode !== 200) {
    throw new Error(`Login failed: ${response.statusCode} ${response.body}`);
  }

  const data = JSON.parse(response.body);
  return data.token;
}

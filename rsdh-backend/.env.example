# App
NODE_ENV="development"
PORT=3001
APP_NAME=my-app
APP_URL=http://localhost:3000

DATABASE_URL="postgresql://user:pass@127.0.0.1:15432/rsdh_dev"

# CORS
CORS_ORIGIN="http://localhost:3000"

ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD=""
TEST_EMAIL=""
TEST_PASSWORD=""

SUPPORT_EMAIL=<EMAIL>

# Email Configuration; use APP_URL, APP_NAME
EMAIL_HOST=mail.example.com
EMAIL_PORT=465
EMAIL_USER=<EMAIL>
EMAIL_PASS=qafjeojeorjeorjape
EMAIL_FROM=<EMAIL>

# OAuth Configuration
GITHUB_CLIENT_ID=
GITHUB_CLIENT_SECRET=
GITHUB_CALLBACK_URL=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL=


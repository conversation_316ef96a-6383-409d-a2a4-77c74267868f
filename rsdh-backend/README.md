# Fastify Prisma User Management API

A robust user management API built with Fastify, Prisma, and TypeScript, featuring comprehensive authentication, user profiles, and admin functionality.

## Features

### ✅ Implemented Features

1. **Automatic Account Initialization**
   - Admin account auto-creation on startup
   - Test account auto-creation (optional)
   - Skips creation if accounts already exist
   - Updates existing users to admin role if needed

2. **Enhanced User Model**
   - Avatar URL support
   - User disabled/enabled flag
   - Role-based access (user/admin)
   - Email verification status
   - Comprehensive user profiles

3. **Authentication & Security**
   - Email/password registration and login
   - Password reset functionality (configured)
   - Session management with better-auth
   - Password hashing with bcrypt
   - JWT token support

4. **User Management API**
   - Get user profile
   - Update user profile
   - List all users (paginated)
   - Toggle user status (enable/disable)
   - Update user roles
   - Admin-only endpoints

5. **Comprehensive Testing**
   - Unit tests for all services
   - Integration tests for API routes
   - Authentication endpoint tests
   - Validation utility tests
   - Test coverage reporting

## Project Structure

```
src/
├── app.ts                 # Main application setup
├── index.ts              # Application entry point
├── config/
│   ├── env.ts            # Environment configuration
│   └── swagger.ts        # API documentation
├── lib/
│   ├── auth.ts           # Better-auth configuration
│   └── prisma.ts         # Prisma client setup
├── routes/
│   └── users.ts          # User management routes
├── services/
│   ├── userService.ts    # User business logic
│   └── initService.ts    # Startup initialization
├── types/
│   └── user.ts           # TypeScript type definitions
└── utils/               # Utility functions

tests/
├── setup.ts             # Test configuration
├── app.test.ts          # Application tests
├── auth/
│   └── auth.test.ts     # Authentication tests
├── routes/
│   └── users.test.ts    # Route integration tests
├── services/
│   ├── userService.test.ts
│   └── initService.test.ts
└── utils/
    └── validation.test.ts
```

## Database Schema

### User Model
```prisma
model User {
  id              String             @id @default(uuid())
  email           String             @unique
  name            String?
  avatar          String?            // User avatar URL
  emailVerified   Boolean            @default(false)
  disabled        Boolean            @default(false) // User disabled flag
  password        String
  role            String             @default("user") // user, admin
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt
  sessions        Session[]
  refreshTokens   RefreshToken[]
  resetTokens     PasswordResetToken[]
}
```

## API Endpoints

### Authentication
- `POST /api/auth/sign-up/email` - Register new user
- `POST /api/auth/sign-in/email` - User login
- `POST /api/auth/sign-out` - User logout
- `POST /api/auth/forget-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password
- `GET /api/auth/session` - Get current session

### User Management
- `GET /api/users/profile` - Get current user profile
- `PUT /api/users/profile` - Update current user profile
- `GET /api/users` - List all users (admin only)
- `GET /api/users/:id` - Get user by ID
- `PATCH /api/users/:id/status` - Toggle user status (admin only)
- `PATCH /api/users/:id/role` - Update user role (admin only)

### System
- `GET /health` - Health check
- `GET /documentation` - Swagger UI
- `GET /documentation/json` - OpenAPI specification

## Setup & Installation

### Prerequisites
- Node.js 18+
- PostgreSQL database
- pnpm (recommended) or npm

### Installation

1. **Clone and install dependencies**
```bash
git clone <repository>
cd fastify-prisma-app
pnpm install
```

2. **Environment Configuration**
Create a `.env` file:
```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/database_name"

# Admin Account (required)
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="your-secure-password"

# Test Account (optional)
TEST_EMAIL="<EMAIL>"
TEST_PASSWORD="test-password"

# Security
JWT_SECRET="your-jwt-secret-key"

# CORS
CORS_ORIGIN="http://localhost:3000"
TRUSTED_ORIGINS="http://localhost:3000"
```

3. **Database Setup**
```bash
# Generate Prisma client
pnpm prisma:generate

# Run database migrations
pnpm prisma:migrate

# (Optional) Open Prisma Studio
pnpm prisma:studio
```

4. **Start Development Server**
```bash
pnpm dev
```

The server will start at `http://localhost:3001`

## Testing

### Run All Tests
```bash
pnpm test
```

### Run Tests with UI
```bash
pnpm test:ui
```

### Run Tests Once
```bash
pnpm test:run
```

### Generate Coverage Report
```bash
pnpm test:coverage
```

### Test Categories

1. **Unit Tests** - Service layer logic
2. **Integration Tests** - API endpoint testing
3. **Validation Tests** - Input validation utilities

**Note**: Database-dependent tests require a test database. If no database is available, only validation tests will run.

## Development Scripts

```bash
pnpm dev          # Start development server with hot reload
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
pnpm test         # Run tests in watch mode
pnpm test:run     # Run tests once
pnpm test:ui      # Run tests with UI
pnpm test:coverage # Generate coverage report
```

## Features in Detail

### Automatic Account Initialization
On startup, the application automatically:
- Checks if admin account exists
- Creates admin account if missing
- Updates existing user to admin role if needed
- Creates test account if configured
- Logs all initialization steps

### User Profile Management
Users can:
- Update their name and avatar
- View their profile information
- Admins can manage all users

### Role-Based Access
- **User**: Basic profile management
- **Admin**: Full user management capabilities

### Security Features
- Password hashing with bcrypt (12 rounds)
- JWT-based authentication
- Session management
- CORS protection
- Security headers with Helmet

## API Documentation

When running, visit:
- Swagger UI: `http://localhost:3001/documentation`
- OpenAPI JSON: `http://localhost:3001/documentation/json`

## Contributing

1. Follow TypeScript best practices
2. Write tests for new features
3. Update documentation
4. Ensure all tests pass before submitting

## License

MIT License

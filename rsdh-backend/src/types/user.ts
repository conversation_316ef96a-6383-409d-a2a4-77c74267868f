export interface User {
  id: string;
  email: string;
  name?: string | null;
  avatar?: string | null;
  emailVerified: boolean;
  disabled: boolean;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserInput {
  email: string;
  password: string;
  name?: string;
  avatar?: string;
  role?: string;
}

export interface UpdateUserInput {
  name?: string;
  avatar?: string;
  disabled?: boolean;
  role?: string;
}

export interface UserResponse {
  id: string;
  email: string;
  name?: string | null;
  avatar?: string | null;
  emailVerified: boolean;
  disabled: boolean;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface LoginInput {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterInput {
  email: string;
  password: string;
  name: string;
  avatar?: string;
}

export interface ResetPasswordInput {
  token: string;
  password: string;
}

export interface ForgotPasswordInput {
  email: string;
}

export type UserRole = 'user' | 'admin';

import { PrismaClient } from '@prisma/client';

// Initialize Prisma Client with DATABASE_URL from environment
// Note: This will be overridden by better-auth's prismaAdapter when used in auth context
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/rsdh_dev'
    }
  },
  log: process.env.NODE_ENV === 'development'
    ? ['query', 'info', 'warn', 'error']
    : ['warn', 'error'],
});

export { prisma };

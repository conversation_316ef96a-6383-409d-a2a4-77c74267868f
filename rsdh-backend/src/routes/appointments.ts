import { FastifyInstance } from 'fastify';
import { AppointmentService, CreateAppointmentData, UpdateAppointmentData } from '../services/appointmentService';
import { TutorService } from '../services/tutorService';
import { authenticateUser, AuthenticatedRequest } from '../middleware/auth';

export async function appointmentRoutes(fastify: FastifyInstance) {
  // Create appointment (student books a tutor)
  fastify.post('/', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Appointments'],
      summary: 'Create appointment',
      description: 'Book an appointment with a tutor',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['tutorId', 'startTime', 'endTime', 'meetingType'],
        properties: {
          tutorId: { type: 'string', description: 'Tutor ID' },
          startTime: { type: 'string', format: 'date-time', description: 'Appointment start time' },
          endTime: { type: 'string', format: 'date-time', description: 'Appointment end time' },
          meetingType: { type: 'string', enum: ['online', 'in_person'], description: 'Meeting type' },
          meetingLink: { type: 'string', description: 'Meeting link (required for online meetings)' },
          notes: { type: 'string', description: 'Additional notes' }
        }
      },
      response: {
        201: {
          description: 'Appointment created successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            tutorId: { type: 'string' },
            studentId: { type: 'string' },
            startTime: { type: 'string', format: 'date-time' },
            endTime: { type: 'string', format: 'date-time' },
            meetingType: { type: 'string' },
            meetingLink: { type: 'string' },
            notes: { type: 'string' },
            status: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        400: {
          description: 'Bad Request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const appointmentData = request.body as Omit<CreateAppointmentData, 'studentId'>;

      const appointment = await AppointmentService.createAppointment({
        ...appointmentData,
        studentId: request.user!.id,
        startTime: new Date(appointmentData.startTime),
        endTime: new Date(appointmentData.endTime)
      });

      return reply.status(201).send(appointment);
    } catch (error) {
      request.log.error('Error creating appointment:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Tutor not found',
          'Tutor is not approved',
          'Student not found',
          'Start time must be before end time',
          'Appointment must be scheduled for future time',
          'Tutor is not available at the requested time',
          'Appointment time is outside tutor\'s available hours',
          'Meeting link is required for online appointments'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to create appointment'
      });
    }
  });

  // Get appointment by ID
  fastify.get('/:appointmentId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Appointments'],
      summary: 'Get appointment by ID',
      description: 'Get detailed information about a specific appointment',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['appointmentId'],
        properties: {
          appointmentId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Appointment details',
          type: 'object',
          properties: {
            id: { type: 'string' },
            tutorId: { type: 'string' },
            studentId: { type: 'string' },
            startTime: { type: 'string', format: 'date-time' },
            endTime: { type: 'string', format: 'date-time' },
            meetingType: { type: 'string' },
            meetingLink: { type: 'string' },
            notes: { type: 'string' },
            status: { type: 'string' },
            tutor: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                title: { type: 'string' },
                rate: { type: 'number' },
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    email: { type: 'string' },
                    avatar: { type: 'string' }
                  }
                }
              }
            },
            student: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                email: { type: 'string' },
                avatar: { type: 'string' }
              }
            },
            review: {
              type: 'object',
              nullable: true,
              properties: {
                id: { type: 'string' },
                rating: { type: 'integer' },
                comment: { type: 'string' }
              }
            }
          }
        },
        404: {
          description: 'Appointment not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { appointmentId } = request.params as { appointmentId: string };
      const appointment = await AppointmentService.getAppointmentById(appointmentId);

      if (!appointment) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Appointment not found'
        });
      }

      // Check if user is authorized to view this appointment
      if (appointment.studentId !== request.user!.id && appointment.tutor.user.id !== request.user!.id) {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'You are not authorized to view this appointment'
        });
      }

      return reply.send(appointment);
    } catch (error) {
      request.log.error('Error getting appointment:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get appointment'
      });
    }
  });

  // Get user's appointments (as student)
  fastify.get('/student/my-appointments', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Appointments'],
      summary: 'Get my appointments as student',
      description: 'Get all appointments for the current user as a student',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['scheduled', 'completed', 'cancelled'] },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' }
        }
      },
      response: {
        200: {
          description: 'List of appointments',
          type: 'object',
          properties: {
            appointments: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  startTime: { type: 'string', format: 'date-time' },
                  endTime: { type: 'string', format: 'date-time' },
                  meetingType: { type: 'string' },
                  status: { type: 'string' },
                  tutor: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      title: { type: 'string' },
                      rate: { type: 'number' },
                      user: {
                        type: 'object',
                        properties: {
                          name: { type: 'string' },
                          avatar: { type: 'string' }
                        }
                      }
                    }
                  }
                }
              }
            },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
            totalPages: { type: 'integer' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { status, page, limit, startDate, endDate } = request.query as {
        status?: string;
        page?: number;
        limit?: number;
        startDate?: string;
        endDate?: string;
      };

      const options: any = {
        status,
        page: page || 1,
        limit: limit || 10
      };

      if (startDate) {
        options.startDate = new Date(startDate);
      }
      if (endDate) {
        options.endDate = new Date(endDate);
      }

      const result = await AppointmentService.getStudentAppointments(request.user!.id, options);
      return reply.send(result);
    } catch (error) {
      request.log.error('Error getting student appointments:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get appointments'
      });
    }
  });

  // Get tutor's appointments
  fastify.get('/tutor/my-appointments', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Appointments'],
      summary: 'Get my appointments as tutor',
      description: 'Get all appointments for the current user as a tutor',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['scheduled', 'completed', 'cancelled'] },
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' }
        }
      },
      response: {
        200: {
          description: 'List of appointments',
          type: 'object',
          properties: {
            appointments: { type: 'array' },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
            totalPages: { type: 'integer' }
          }
        },
        404: {
          description: 'Tutor profile not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      // Get tutor profile first
      const tutorProfile = await TutorService.getTutorByUserId(request.user!.id);

      if (!tutorProfile) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Tutor profile not found'
        });
      }

      const { status, page, limit, startDate, endDate } = request.query as {
        status?: string;
        page?: number;
        limit?: number;
        startDate?: string;
        endDate?: string;
      };

      const options: any = {
        status,
        page: page || 1,
        limit: limit || 10
      };

      if (startDate) {
        options.startDate = new Date(startDate);
      }
      if (endDate) {
        options.endDate = new Date(endDate);
      }

      const result = await AppointmentService.getTutorAppointments(tutorProfile.id, options);
      return reply.send(result);
    } catch (error) {
      request.log.error('Error getting tutor appointments:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get appointments'
      });
    }
  });

  // Update appointment
  fastify.put('/:appointmentId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Appointments'],
      summary: 'Update appointment',
      description: 'Update an existing appointment',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['appointmentId'],
        properties: {
          appointmentId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          startTime: { type: 'string', format: 'date-time' },
          endTime: { type: 'string', format: 'date-time' },
          meetingType: { type: 'string', enum: ['online', 'in_person'] },
          meetingLink: { type: 'string' },
          notes: { type: 'string' },
          status: { type: 'string', enum: ['scheduled', 'completed', 'cancelled'] }
        }
      },
      response: {
        200: {
          description: 'Appointment updated successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            tutorId: { type: 'string' },
            studentId: { type: 'string' },
            startTime: { type: 'string', format: 'date-time' },
            endTime: { type: 'string', format: 'date-time' },
            meetingType: { type: 'string' },
            meetingLink: { type: 'string' },
            notes: { type: 'string' },
            status: { type: 'string' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        403: {
          description: 'Forbidden',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        404: {
          description: 'Appointment not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { appointmentId } = request.params as { appointmentId: string };
      const updateData = request.body as UpdateAppointmentData;

      // Get appointment to check authorization
      const appointment = await AppointmentService.getAppointmentById(appointmentId);

      if (!appointment) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Appointment not found'
        });
      }

      // Check if user is authorized to update this appointment
      if (appointment.studentId !== request.user!.id && appointment.tutor.user.id !== request.user!.id) {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'You are not authorized to update this appointment'
        });
      }

      // Convert date strings to Date objects
      const processedUpdateData: UpdateAppointmentData = {
        ...updateData
      };

      if (updateData.startTime) {
        processedUpdateData.startTime = new Date(updateData.startTime);
      }
      if (updateData.endTime) {
        processedUpdateData.endTime = new Date(updateData.endTime);
      }

      const updatedAppointment = await AppointmentService.updateAppointment(appointmentId, processedUpdateData);

      return reply.send(updatedAppointment);
    } catch (error) {
      request.log.error('Error updating appointment:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Appointment not found',
          'Start time must be before end time',
          'Cannot reschedule to past time',
          'Tutor is not available at the requested time',
          'Meeting link is required for online appointments'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to update appointment'
      });
    }
  });

  // Cancel appointment
  fastify.patch('/:appointmentId/cancel', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Appointments'],
      summary: 'Cancel appointment',
      description: 'Cancel an existing appointment',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['appointmentId'],
        properties: {
          appointmentId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Appointment cancelled successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        403: {
          description: 'Forbidden',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        404: {
          description: 'Appointment not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { appointmentId } = request.params as { appointmentId: string };

      // Get appointment to check authorization
      const appointment = await AppointmentService.getAppointmentById(appointmentId);

      if (!appointment) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Appointment not found'
        });
      }

      // Check if user is authorized to cancel this appointment
      if (appointment.studentId !== request.user!.id && appointment.tutor.user.id !== request.user!.id) {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'You are not authorized to cancel this appointment'
        });
      }

      const cancelledAppointment = await AppointmentService.cancelAppointment(appointmentId);

      return reply.send(cancelledAppointment);
    } catch (error) {
      request.log.error('Error cancelling appointment:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Appointment not found',
          'Appointment is already cancelled',
          'Cannot cancel completed appointment'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to cancel appointment'
      });
    }
  });

  // Complete appointment (tutor only)
  fastify.patch('/:appointmentId/complete', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Appointments'],
      summary: 'Complete appointment',
      description: 'Mark an appointment as completed (tutor only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['appointmentId'],
        properties: {
          appointmentId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Appointment completed successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        403: {
          description: 'Forbidden',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        404: {
          description: 'Appointment not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { appointmentId } = request.params as { appointmentId: string };

      // Get appointment to check authorization
      const appointment = await AppointmentService.getAppointmentById(appointmentId);

      if (!appointment) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Appointment not found'
        });
      }

      // Only tutor can complete appointment
      if (appointment.tutor.user.id !== request.user!.id) {
        return reply.status(403).send({
          error: 'Forbidden',
          message: 'Only the tutor can complete this appointment'
        });
      }

      const completedAppointment = await AppointmentService.completeAppointment(appointmentId);

      return reply.send(completedAppointment);
    } catch (error) {
      request.log.error('Error completing appointment:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Appointment not found',
          'Appointment is already completed',
          'Cannot complete cancelled appointment'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to complete appointment'
      });
    }
  });

  // Get available time slots for a tutor
  fastify.get('/tutor/:tutorId/available-slots', {
    schema: {
      tags: ['Appointments'],
      summary: 'Get available time slots',
      description: 'Get available time slots for a tutor on a specific date',
      params: {
        type: 'object',
        required: ['tutorId'],
        properties: {
          tutorId: { type: 'string' }
        }
      },
      querystring: {
        type: 'object',
        required: ['date'],
        properties: {
          date: { type: 'string', format: 'date', description: 'Date to check availability (YYYY-MM-DD)' },
          duration: { type: 'integer', minimum: 15, maximum: 480, default: 60, description: 'Duration in minutes' }
        }
      },
      response: {
        200: {
          description: 'Available time slots',
          type: 'array',
          items: {
            type: 'object',
            properties: {
              startTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' },
              endTime: { type: 'string', pattern: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$' }
            }
          }
        },
        404: {
          description: 'Tutor not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { tutorId } = request.params as { tutorId: string };
      const { date, duration } = request.query as { date: string; duration?: number };

      // Check if tutor exists
      const tutor = await TutorService.getTutorById(tutorId);
      if (!tutor) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Tutor not found'
        });
      }

      const availableSlots = await AppointmentService.getAvailableTimeSlots(
        tutorId,
        new Date(date),
        duration || 60
      );

      return reply.send(availableSlots);
    } catch (error) {
      request.log.error('Error getting available slots:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get available time slots'
      });
    }
  });
}

import { FastifyInstance } from 'fastify';
import { ReviewService, CreateReviewData, UpdateReviewData } from '../services/reviewService';
import { authenticateUser, requireAdmin, AuthenticatedRequest } from '../middleware/auth';

export async function reviewRoutes(fastify: FastifyInstance) {
  // Create review for a completed appointment
  fastify.post('/', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Reviews'],
      summary: 'Create review',
      description: 'Create a review for a completed appointment',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['appointmentId', 'tutorId', 'rating'],
        properties: {
          appointmentId: { type: 'string', description: 'Appointment ID' },
          tutorId: { type: 'string', description: 'Tutor ID' },
          rating: { type: 'integer', minimum: 1, maximum: 5, description: 'Rating from 1 to 5' },
          comment: { type: 'string', description: 'Optional comment' }
        }
      },
      response: {
        201: {
          description: 'Review created successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            appointmentId: { type: 'string' },
            tutorId: { type: 'string' },
            rating: { type: 'integer' },
            comment: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        400: {
          description: 'Bad Request',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const reviewData = request.body as CreateReviewData;

      const review = await ReviewService.createReview(reviewData, request.user!.id);

      return reply.status(201).send(review);
    } catch (error) {
      request.log.error('Error creating review:', error);

      if (error instanceof Error) {
        const errorMessages = [
          'Rating must be between 1 and 5',
          'Appointment not found',
          'Can only review completed appointments',
          'You can only review your own appointments',
          'Tutor ID does not match appointment',
          'Review already exists for this appointment'
        ];

        if (errorMessages.includes(error.message)) {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to create review'
      });
    }
  });

  // Get review by ID
  fastify.get('/:reviewId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Reviews'],
      summary: 'Get review by ID',
      description: 'Get detailed information about a specific review',
      params: {
        type: 'object',
        required: ['reviewId'],
        properties: {
          reviewId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Review details',
          type: 'object',
          properties: {
            id: { type: 'string' },
            appointmentId: { type: 'string' },
            tutorId: { type: 'string' },
            rating: { type: 'integer' },
            comment: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            appointment: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                startTime: { type: 'string', format: 'date-time' },
                endTime: { type: 'string', format: 'date-time' },
                meetingType: { type: 'string' },
                student: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    email: { type: 'string' },
                    avatar: { type: 'string' }
                  }
                }
              }
            },
            tutor: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                title: { type: 'string' },
                user: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    email: { type: 'string' },
                    avatar: { type: 'string' }
                  }
                }
              }
            }
          }
        },
        404: {
          description: 'Review not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { reviewId } = request.params as { reviewId: string };
      const review = await ReviewService.getReviewById(reviewId);

      if (!review) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Review not found'
        });
      }

      return reply.send(review);
    } catch (error) {
      request.log.error('Error getting review:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get review'
      });
    }
  });

  // Get reviews for a tutor
  fastify.get('/tutor/:tutorId', {
    schema: {
      tags: ['Reviews'],
      summary: 'Get tutor reviews',
      description: 'Get all reviews for a specific tutor',
      params: {
        type: 'object',
        required: ['tutorId'],
        properties: {
          tutorId: { type: 'string' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          rating: { type: 'integer', minimum: 1, maximum: 5 }
        }
      },
      response: {
        200: {
          description: 'List of reviews',
          type: 'object',
          properties: {
            reviews: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  rating: { type: 'integer' },
                  comment: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' },
                  appointment: {
                    type: 'object',
                    properties: {
                      startTime: { type: 'string', format: 'date-time' },
                      student: {
                        type: 'object',
                        properties: {
                          name: { type: 'string' },
                          avatar: { type: 'string' }
                        }
                      }
                    }
                  }
                }
              }
            },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
            totalPages: { type: 'integer' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { tutorId } = request.params as { tutorId: string };
      const { page, limit, rating } = request.query as {
        page?: number;
        limit?: number;
        rating?: number;
      };

      const result = await ReviewService.getTutorReviews(tutorId, {
        page: page || 1,
        limit: limit || 10,
        rating
      });

      return reply.send(result);
    } catch (error) {
      request.log.error('Error getting tutor reviews:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get tutor reviews'
      });
    }
  });

  // Get tutor review statistics
  fastify.get('/tutor/:tutorId/stats', {
    schema: {
      tags: ['Reviews'],
      summary: 'Get tutor review statistics',
      description: 'Get review statistics for a specific tutor',
      params: {
        type: 'object',
        required: ['tutorId'],
        properties: {
          tutorId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Tutor review statistics',
          type: 'object',
          properties: {
            totalReviews: { type: 'integer' },
            averageRating: { type: 'number' },
            ratingDistribution: {
              type: 'object',
              properties: {
                1: { type: 'integer' },
                2: { type: 'integer' },
                3: { type: 'integer' },
                4: { type: 'integer' },
                5: { type: 'integer' }
              }
            },
            recentReviews: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  rating: { type: 'integer' },
                  comment: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { tutorId } = request.params as { tutorId: string };

      const stats = await ReviewService.getTutorReviewStats(tutorId);

      return reply.send(stats);
    } catch (error) {
      request.log.error('Error getting tutor review stats:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get tutor review statistics'
      });
    }
  });

  // Get current user's reviews (as student)
  fastify.get('/student/my-reviews', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Reviews'],
      summary: 'Get my reviews as student',
      description: 'Get all reviews created by the current user',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
        }
      },
      response: {
        200: {
          description: 'List of reviews',
          type: 'object',
          properties: {
            reviews: { type: 'array' },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
            totalPages: { type: 'integer' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { page, limit } = request.query as {
        page?: number;
        limit?: number;
      };

      const result = await ReviewService.getStudentReviews(request.user!.id, {
        page: page || 1,
        limit: limit || 10
      });

      return reply.send(result);
    } catch (error) {
      request.log.error('Error getting student reviews:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get student reviews'
      });
    }
  });

  // Update review
  fastify.put('/:reviewId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Reviews'],
      summary: 'Update review',
      description: 'Update an existing review',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['reviewId'],
        properties: {
          reviewId: { type: 'string' }
        }
      },
      body: {
        type: 'object',
        properties: {
          rating: { type: 'integer', minimum: 1, maximum: 5 },
          comment: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Review updated successfully',
          type: 'object',
          properties: {
            id: { type: 'string' },
            appointmentId: { type: 'string' },
            tutorId: { type: 'string' },
            rating: { type: 'integer' },
            comment: { type: 'string' },
            updatedAt: { type: 'string', format: 'date-time' }
          }
        },
        403: {
          description: 'Forbidden',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        404: {
          description: 'Review not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { reviewId } = request.params as { reviewId: string };
      const updateData = request.body as UpdateReviewData;

      const updatedReview = await ReviewService.updateReview(reviewId, updateData, request.user!.id);

      return reply.send(updatedReview);
    } catch (error) {
      request.log.error('Error updating review:', error);

      if (error instanceof Error) {
        if (error.message === 'Review not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }
        if (error.message === 'You can only update your own reviews' ||
            error.message === 'Rating must be between 1 and 5') {
          return reply.status(400).send({
            error: 'Bad Request',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to update review'
      });
    }
  });

  // Delete review
  fastify.delete('/:reviewId', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Reviews'],
      summary: 'Delete review',
      description: 'Delete an existing review',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['reviewId'],
        properties: {
          reviewId: { type: 'string' }
        }
      },
      response: {
        204: {
          description: 'Review deleted successfully'
        },
        403: {
          description: 'Forbidden',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        },
        404: {
          description: 'Review not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: AuthenticatedRequest, reply) => {
    try {
      const { reviewId } = request.params as { reviewId: string };

      await ReviewService.deleteReview(reviewId, request.user!.id);

      return reply.status(204).send();
    } catch (error) {
      request.log.error('Error deleting review:', error);

      if (error instanceof Error) {
        if (error.message === 'Review not found') {
          return reply.status(404).send({
            error: 'Not Found',
            message: error.message
          });
        }
        if (error.message === 'You can only delete your own reviews') {
          return reply.status(403).send({
            error: 'Forbidden',
            message: error.message
          });
        }
      }

      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to delete review'
      });
    }
  });

  // Get review by appointment ID
  fastify.get('/appointment/:appointmentId', {
    schema: {
      tags: ['Reviews'],
      summary: 'Get review by appointment ID',
      description: 'Get review for a specific appointment',
      params: {
        type: 'object',
        required: ['appointmentId'],
        properties: {
          appointmentId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'Review details',
          type: 'object'
        },
        404: {
          description: 'Review not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { appointmentId } = request.params as { appointmentId: string };
      const review = await ReviewService.getReviewByAppointmentId(appointmentId);

      if (!review) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'Review not found for this appointment'
        });
      }

      return reply.send(review);
    } catch (error) {
      request.log.error('Error getting review by appointment:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get review'
      });
    }
  });

  // Admin: Get all reviews
  fastify.get('/admin/all', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Reviews', 'Admin'],
      summary: 'Get all reviews (Admin)',
      description: 'Get all reviews with filtering options (admin only)',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          rating: { type: 'integer', minimum: 1, maximum: 5 },
          tutorId: { type: 'string' },
          studentId: { type: 'string' }
        }
      },
      response: {
        200: {
          description: 'List of all reviews',
          type: 'object',
          properties: {
            reviews: { type: 'array' },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
            totalPages: { type: 'integer' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { page, limit, rating, tutorId, studentId } = request.query as {
        page?: number;
        limit?: number;
        rating?: number;
        tutorId?: string;
        studentId?: string;
      };

      const result = await ReviewService.getAllReviews({
        page: page || 1,
        limit: limit || 10,
        rating,
        tutorId,
        studentId
      });

      return reply.send(result);
    } catch (error) {
      request.log.error('Error getting all reviews:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get reviews'
      });
    }
  });

  // Admin: Get review statistics
  fastify.get('/admin/statistics', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Reviews', 'Admin'],
      summary: 'Get review statistics (Admin)',
      description: 'Get overall review statistics (admin only)',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'Review statistics',
          type: 'object',
          properties: {
            totalReviews: { type: 'integer' },
            averageRating: { type: 'number' },
            ratingDistribution: {
              type: 'object',
              properties: {
                1: { type: 'integer' },
                2: { type: 'integer' },
                3: { type: 'integer' },
                4: { type: 'integer' },
                5: { type: 'integer' }
              }
            },
            reviewsThisMonth: { type: 'integer' },
            reviewsLastMonth: { type: 'integer' }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const stats = await ReviewService.getReviewStatistics();
      return reply.send(stats);
    } catch (error) {
      request.log.error('Error getting review statistics:', error);
      return reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get review statistics'
      });
    }
  });
}

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { UserService } from '../services/userService';
import { UpdateUserInput } from '../types/user';
import { authenticateUser, requireAdmin, AuthenticatedRequest } from '../middleware/auth';

interface GetUserParams {
  id: string;
}

interface UpdateUserBody extends UpdateUserInput {}

interface GetUsersQuery {
  page?: number;
  limit?: number;
}

interface ToggleUserStatusBody {
  disabled: boolean;
}

interface UpdateUserRoleBody {
  role: string;
}

export async function userRoutes(fastify: FastifyInstance) {
  // Get current user profile
  fastify.get('/profile', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Users'],
      summary: 'Get current user profile',
      description: 'Get the profile of the currently authenticated user',
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'User profile',
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            avatar: { type: 'string' },
            emailVerified: { type: 'boolean' },
            disabled: { type: 'boolean' },
            role: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        401: {
          description: 'Unauthorized',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const user = await UserService.getUserById(request.user!.id);
      if (!user) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'User not found'
        });
      }

      reply.send({
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        emailVerified: user.emailVerified,
        disabled: user.disabled,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      });
    } catch (error) {
      request.log.error('Error getting user profile:', error);
      reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to get user profile'
      });
    }
  });

  // Update current user profile
  fastify.put('/profile', {
    preHandler: authenticateUser,
    schema: {
      tags: ['Users'],
      summary: 'Update current user profile',
      description: 'Update the profile of the currently authenticated user',
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          avatar: { type: 'string', format: 'uri' },
        },
      },
      response: {
        200: {
          description: 'Updated user profile',
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            avatar: { type: 'string' },
            emailVerified: { type: 'boolean' },
            disabled: { type: 'boolean' },
            role: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        401: {
          description: 'Unauthorized',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: AuthenticatedRequest<{ Body: UpdateUserBody }>, reply: FastifyReply) => {
    try {
      const { name, avatar } = request.body;

      const updatedUser = await UserService.updateUser(request.user!.id, {
        name,
        avatar,
      });

      reply.send({
        id: updatedUser.id,
        email: updatedUser.email,
        name: updatedUser.name,
        avatar: updatedUser.avatar,
        emailVerified: updatedUser.emailVerified,
        disabled: updatedUser.disabled,
        role: updatedUser.role,
        createdAt: updatedUser.createdAt,
        updatedAt: updatedUser.updatedAt,
      });
    } catch (error) {
      request.log.error('Error updating user profile:', error);
      reply.status(500).send({
        error: 'Internal Server Error',
        message: 'Failed to update user profile'
      });
    }
  });

  // Get user by ID (admin only)
  fastify.get('/:id', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Users'],
      summary: 'Get user by ID',
      description: 'Get a user by their ID (admin only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      response: {
        200: {
          description: 'User details',
          type: 'object',
          properties: {
            id: { type: 'string' },
            email: { type: 'string' },
            name: { type: 'string' },
            avatar: { type: 'string' },
            emailVerified: { type: 'boolean' },
            disabled: { type: 'boolean' },
            role: { type: 'string' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
          },
        },
        404: {
          description: 'User not found',
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' },
          },
        },
      },
    },
  }, async (request: AuthenticatedRequest<{ Params: GetUserParams }>, reply: FastifyReply) => {
    try {
      const user = await UserService.getUserById(request.params.id);

      if (!user) {
        return reply.status(404).send({
          error: 'Not Found',
          message: 'User not found',
        });
      }

      reply.send({
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        emailVerified: user.emailVerified,
        disabled: user.disabled,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      });
    } catch (error) {
      request.log.error('Error getting user by ID:', error);
      reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while fetching the user',
      });
    }
  });

  // Get all users (admin only)
  fastify.get('/', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Users'],
      summary: 'Get all users',
      description: 'Get a paginated list of all users (admin only)',
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
        },
      },
      response: {
        200: {
          description: 'List of users',
          type: 'object',
          properties: {
            users: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  email: { type: 'string' },
                  name: { type: 'string' },
                  avatar: { type: 'string' },
                  emailVerified: { type: 'boolean' },
                  disabled: { type: 'boolean' },
                  role: { type: 'string' },
                  createdAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' },
                },
              },
            },
            total: { type: 'integer' },
            page: { type: 'integer' },
            limit: { type: 'integer' },
          },
        },
      },
    },
  }, async (request: AuthenticatedRequest<{ Querystring: GetUsersQuery }>, reply: FastifyReply) => {
    try {
      const { page = 1, limit = 10 } = request.query;
      const result = await UserService.getAllUsers(page, limit);

      reply.send({
        ...result,
        page,
        limit,
      });
    } catch (error) {
      request.log.error('Error getting all users:', error);
      reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while fetching users',
      });
    }
  });

  // Toggle user status (admin only)
  fastify.patch('/:id/status', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Users'],
      summary: 'Toggle user status',
      description: 'Enable or disable a user account (admin only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: {
        type: 'object',
        properties: {
          disabled: { type: 'boolean' },
        },
        required: ['disabled'],
      },
    },
  }, async (request: AuthenticatedRequest<{ Params: GetUserParams; Body: ToggleUserStatusBody }>, reply: FastifyReply) => {
    try {
      const user = await UserService.toggleUserStatus(request.params.id, request.body.disabled);
      reply.send(user);
    } catch (error) {
      request.log.error('Error toggling user status:', error);
      reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while updating user status',
      });
    }
  });

  // Update user role (admin only)
  fastify.patch('/:id/role', {
    preHandler: [authenticateUser, requireAdmin],
    schema: {
      tags: ['Users'],
      summary: 'Update user role',
      description: 'Update a user\'s role (admin only)',
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: {
        type: 'object',
        properties: {
          role: { type: 'string', enum: ['user', 'admin'] },
        },
        required: ['role'],
      },
    },
  }, async (request: AuthenticatedRequest<{ Params: GetUserParams; Body: UpdateUserRoleBody }>, reply: FastifyReply) => {
    try {
      const user = await UserService.updateUserRole(request.params.id, request.body.role);
      reply.send(user);
    } catch (error) {
      request.log.error('Error updating user role:', error);
      reply.status(500).send({
        error: 'Internal Server Error',
        message: 'An error occurred while updating user role',
      });
    }
  });
}

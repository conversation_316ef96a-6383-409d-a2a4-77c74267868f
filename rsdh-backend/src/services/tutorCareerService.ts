import { prisma } from '../lib/prisma';
import { TutorCareer } from '@prisma/client';

export interface CreateCareerData {
  tutorId: string;
  title: string;
  company: string;
  startYear: number;
  endYear?: number;
  current: boolean;
  description?: string;
}

export interface UpdateCareerData {
  title?: string;
  company?: string;
  startYear?: number;
  endYear?: number;
  current?: boolean;
  description?: string;
}

export class TutorCareerService {
  /**
   * Add career record for a tutor
   */
  static async addCareer(data: CreateCareerData): Promise<TutorCareer> {
    // Validate years
    const currentYear = new Date().getFullYear();

    if (data.startYear < 1900 || data.startYear > currentYear) {
      throw new Error('Invalid start year');
    }

    if (!data.current && data.endYear && (data.endYear < data.startYear || data.endYear > currentYear)) {
      throw new Error('Invalid end year');
    }

    if (data.current && data.endYear) {
      throw new Error('Current position cannot have an end year');
    }

    if (!data.current && !data.endYear) {
      throw new Error('Non-current position must have an end year');
    }

    // Check if tutor exists
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: data.tutorId }
    });

    if (!tutor) {
      throw new Error('Tutor profile not found');
    }

    // If this is marked as current, update other current positions
    if (data.current) {
      await prisma.tutorCareer.updateMany({
        where: {
          tutorId: data.tutorId,
          current: true
        },
        data: {
          current: false
        }
      });
    }

    return await prisma.tutorCareer.create({
      data
    });
  }

  /**
   * Get all career records for a tutor
   */
  static async getTutorCareer(tutorId: string): Promise<TutorCareer[]> {
    return await prisma.tutorCareer.findMany({
      where: { tutorId },
      orderBy: [
        { current: 'desc' },
        { startYear: 'desc' }
      ]
    });
  }

  /**
   * Get career record by ID
   */
  static async getCareerById(careerId: string): Promise<TutorCareer | null> {
    return await prisma.tutorCareer.findUnique({
      where: { id: careerId }
    });
  }

  /**
   * Update career record
   */
  static async updateCareer(
    careerId: string,
    data: UpdateCareerData
  ): Promise<TutorCareer> {
    const existing = await prisma.tutorCareer.findUnique({
      where: { id: careerId }
    });

    if (!existing) {
      throw new Error('Career record not found');
    }

    // Validate years if provided
    const currentYear = new Date().getFullYear();

    if (data.startYear && (data.startYear < 1900 || data.startYear > currentYear)) {
      throw new Error('Invalid start year');
    }

    const startYear = data.startYear ?? existing.startYear;
    const current = data.current ?? existing.current;

    // If updating to current position, endYear should be null
    // If updating to non-current position, endYear should be provided or kept from existing
    let endYear: number | null;
    if (current) {
      endYear = null; // Current positions don't have end year
    } else {
      endYear = data.endYear ?? existing.endYear;
      if (!endYear) {
        throw new Error('Non-current position must have an end year');
      }
      if (endYear < startYear || endYear > currentYear) {
        throw new Error('Invalid end year');
      }
    }

    // If this is being marked as current, update other current positions
    if (data.current && !existing.current) {
      await prisma.tutorCareer.updateMany({
        where: {
          tutorId: existing.tutorId,
          current: true,
          id: { not: careerId }
        },
        data: {
          current: false
        }
      });
    }

    return await prisma.tutorCareer.update({
      where: { id: careerId },
      data: {
        ...data,
        endYear,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Delete career record
   */
  static async deleteCareer(careerId: string): Promise<void> {
    const existing = await prisma.tutorCareer.findUnique({
      where: { id: careerId }
    });

    if (!existing) {
      throw new Error('Career record not found');
    }

    await prisma.tutorCareer.delete({
      where: { id: careerId }
    });
  }

  /**
   * Bulk update career records for a tutor
   */
  static async bulkUpdateCareer(
    tutorId: string,
    careers: Omit<CreateCareerData, 'tutorId'>[]
  ): Promise<TutorCareer[]> {
    // Validate tutor exists
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: tutorId }
    });

    if (!tutor) {
      throw new Error('Tutor profile not found');
    }

    // Validate all career data
    const currentYear = new Date().getFullYear();
    let currentCount = 0;

    for (const career of careers) {
      if (career.startYear < 1900 || career.startYear > currentYear) {
        throw new Error('Invalid start year');
      }

      if (!career.current && career.endYear && (career.endYear < career.startYear || career.endYear > currentYear)) {
        throw new Error('Invalid end year');
      }

      if (career.current && career.endYear) {
        throw new Error('Current position cannot have an end year');
      }

      if (!career.current && !career.endYear) {
        throw new Error('Non-current position must have an end year');
      }

      if (career.current) {
        currentCount++;
      }
    }

    if (currentCount > 1) {
      throw new Error('Only one position can be marked as current');
    }

    // Use transaction to replace all career records
    return await prisma.$transaction(async (tx) => {
      // Delete existing career records
      await tx.tutorCareer.deleteMany({
        where: { tutorId }
      });

      // Create new career records
      const results: TutorCareer[] = [];
      for (const career of careers) {
        const created = await tx.tutorCareer.create({
          data: {
            ...career,
            tutorId
          }
        });
        results.push(created);
      }

      return results;
    });
  }

  /**
   * Get current position for a tutor
   */
  static async getCurrentPosition(tutorId: string): Promise<TutorCareer | null> {
    return await prisma.tutorCareer.findFirst({
      where: {
        tutorId,
        current: true
      }
    });
  }

  /**
   * Get career records by company
   */
  static async getCareerByCompany(tutorId: string, company: string): Promise<TutorCareer[]> {
    return await prisma.tutorCareer.findMany({
      where: {
        tutorId,
        company: {
          contains: company,
          mode: 'insensitive'
        }
      },
      orderBy: { startYear: 'desc' }
    });
  }

  /**
   * Get career records by title
   */
  static async getCareerByTitle(tutorId: string, title: string): Promise<TutorCareer[]> {
    return await prisma.tutorCareer.findMany({
      where: {
        tutorId,
        title: {
          contains: title,
          mode: 'insensitive'
        }
      },
      orderBy: { startYear: 'desc' }
    });
  }

  /**
   * Calculate total years of experience
   */
  static async getTotalExperience(tutorId: string): Promise<number> {
    const careers = await this.getTutorCareer(tutorId);
    const currentYear = new Date().getFullYear();

    let totalYears = 0;

    for (const career of careers) {
      const endYear = career.current ? currentYear : (career.endYear || currentYear);
      const years = endYear - career.startYear;
      totalYears += Math.max(0, years);
    }

    return totalYears;
  }

  /**
   * Get career summary
   */
  static async getCareerSummary(tutorId: string): Promise<{
    totalExperience: number;
    currentPosition: TutorCareer | null;
    totalPositions: number;
    companies: string[];
  }> {
    const careers = await this.getTutorCareer(tutorId);
    const totalExperience = await this.getTotalExperience(tutorId);
    const currentPosition = await this.getCurrentPosition(tutorId);

    const companies = [...new Set(careers.map(career => career.company))];

    return {
      totalExperience,
      currentPosition,
      totalPositions: careers.length,
      companies
    };
  }

  /**
   * Validate career data
   */
  static validateCareerData(data: CreateCareerData | UpdateCareerData): string[] {
    const errors: string[] = [];
    const currentYear = new Date().getFullYear();

    if ('startYear' in data && data.startYear) {
      if (data.startYear < 1900 || data.startYear > currentYear) {
        errors.push('Start year must be between 1900 and ' + currentYear);
      }
    }

    if ('endYear' in data && data.endYear) {
      if (data.endYear > currentYear) {
        errors.push('End year cannot be in the future');
      }

      if ('startYear' in data && data.startYear && data.endYear < data.startYear) {
        errors.push('End year must be after start year');
      }
    }

    if ('current' in data && data.current && 'endYear' in data && data.endYear) {
      errors.push('Current position cannot have an end year');
    }

    if ('current' in data && data.current === false && !('endYear' in data && data.endYear)) {
      errors.push('Non-current position must have an end year');
    }

    if ('title' in data && data.title && data.title.trim().length < 2) {
      errors.push('Title must be at least 2 characters long');
    }

    if ('company' in data && data.company && data.company.trim().length < 2) {
      errors.push('Company must be at least 2 characters long');
    }

    return errors;
  }
}

import { FastifyInstance } from 'fastify';
import { UserService } from './userService';

export class InitService {
  /**
   * Initialize default admin and test accounts
   */
  static async initializeDefaultAccounts(app: FastifyInstance): Promise<void> {
    // Skip initialization in test environment to avoid conflicts
    if (app.config.NODE_ENV === 'test') {
      app.log.info('🧪 Skipping default accounts initialization in test environment');
      return;
    }

    try {
      app.log.info('🔧 Initializing default accounts...');

      // Initialize admin account
      await this.initializeAdminAccount(app);

      // Initialize test account if configured
      if (app.config.TEST_EMAIL && app.config.TEST_PASSWORD) {
        await this.initializeTestAccount(app);
      }

      app.log.info('✅ Default accounts initialization completed');
    } catch (error) {
      app.log.error('❌ Error initializing default accounts:', error);
      throw error;
    }
  }

  /**
   * Initialize admin account
   */
  private static async initializeAdminAccount(app: FastifyInstance): Promise<void> {
    const adminEmail = app.config.ADMIN_EMAIL;
    const adminPassword = app.config.ADMIN_PASSWORD;

    if (!adminEmail || !adminPassword) {
      app.log.warn('⚠️  Admin email or password not configured, skipping admin account creation');
      return;
    }

    try {
      // Check if admin already exists
      const existingAdmin = await UserService.findByEmail(adminEmail);

      if (existingAdmin) {
        app.log.info(`👤 Admin account already exists: ${adminEmail}`);

        // Update role to admin if it's not already
        if (existingAdmin.role !== 'admin') {
          await UserService.updateUserRole(existingAdmin.id, 'admin');
          app.log.info(`🔄 Updated existing user ${adminEmail} to admin role`);
        }
        return;
      }

      // Create admin account
      const admin = await UserService.createUser({
        email: adminEmail,
        password: adminPassword,
        name: 'Administrator',
        role: 'admin',
      });

      app.log.info(`✅ Admin account created successfully: ${admin.email}`);
    } catch (error) {
      app.log.error(`❌ Error creating admin account: ${error}`);
      throw error;
    }
  }

  /**
   * Initialize test account
   */
  private static async initializeTestAccount(app: FastifyInstance): Promise<void> {
    const testEmail = app.config.TEST_EMAIL;
    const testPassword = app.config.TEST_PASSWORD;

    if (!testEmail || !testPassword) {
      app.log.info('ℹ️  Test account credentials not configured, skipping test account creation');
      return;
    }

    try {
      // Check if test user already exists
      const existingTest = await UserService.findByEmail(testEmail);

      if (existingTest) {
        app.log.info(`👤 Test account already exists: ${testEmail}`);
        return;
      }

      // Create test account
      const testUser = await UserService.createUser({
        email: testEmail,
        password: testPassword,
        name: 'Test User',
        role: 'user',
      });

      app.log.info(`✅ Test account created successfully: ${testUser.email}`);
    } catch (error) {
      app.log.error(`❌ Error creating test account: ${error}`);
      throw error;
    }
  }

  /**
   * Validate environment configuration
   */
  static validateConfiguration(app: FastifyInstance): void {
    const requiredEnvVars = ['ADMIN_EMAIL', 'ADMIN_PASSWORD'];
    const missingVars = requiredEnvVars.filter(varName => !app.config[varName as keyof typeof app.config]);

    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(app.config.ADMIN_EMAIL)) {
      throw new Error('ADMIN_EMAIL must be a valid email address');
    }

    if (app.config.TEST_EMAIL && !emailRegex.test(app.config.TEST_EMAIL)) {
      throw new Error('TEST_EMAIL must be a valid email address');
    }

    // Validate password strength
    if (app.config.ADMIN_PASSWORD.length < 8) {
      throw new Error('ADMIN_PASSWORD must be at least 8 characters long');
    }

    if (app.config.TEST_PASSWORD && app.config.TEST_PASSWORD.length < 8) {
      throw new Error('TEST_PASSWORD must be at least 8 characters long');
    }
  }
}

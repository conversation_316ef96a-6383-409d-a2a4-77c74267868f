import { prisma } from '../lib/prisma';
import { Appointment } from '@prisma/client';

export interface CreateAppointmentData {
  tutorId: string;
  studentId: string;
  startTime: Date;
  endTime: Date;
  meetingType: 'online' | 'in_person';
  meetingLink?: string;
  notes?: string;
}

export interface UpdateAppointmentData {
  startTime?: Date;
  endTime?: Date;
  meetingType?: 'online' | 'in_person';
  meetingLink?: string;
  notes?: string;
  status?: 'scheduled' | 'completed' | 'cancelled';
}

export interface AppointmentWithDetails extends Appointment {
  tutor: {
    id: string;
    title: string | null;
    rate: number | null;
    user: {
      id: string;
      name: string | null;
      email: string;
      avatar: string | null;
    };
  };
  student: {
    id: string;
    name: string | null;
    email: string;
    avatar: string | null;
  };
  review?: {
    id: string;
    rating: number;
    comment: string | null;
  } | null;
}

export class AppointmentService {
  /**
   * Create a new appointment
   */
  static async createAppointment(data: CreateAppointmentData): Promise<Appointment> {
    // Validate tutor exists and is approved
    const tutor = await prisma.tutorProfile.findUnique({
      where: { id: data.tutorId },
      include: { user: true }
    });

    if (!tutor) {
      throw new Error('Tutor not found');
    }

    if (tutor.status !== 'approved') {
      throw new Error('Tutor is not approved');
    }

    // Validate student exists
    const student = await prisma.user.findUnique({
      where: { id: data.studentId }
    });

    if (!student) {
      throw new Error('Student not found');
    }

    // Validate appointment time
    if (data.startTime >= data.endTime) {
      throw new Error('Start time must be before end time');
    }

    if (data.startTime <= new Date()) {
      throw new Error('Appointment must be scheduled for future time');
    }

    // Check for conflicting appointments for the tutor
    const conflictingAppointment = await prisma.appointment.findFirst({
      where: {
        tutorId: data.tutorId,
        status: { in: ['scheduled'] },
        OR: [
          {
            AND: [
              { startTime: { lte: data.startTime } },
              { endTime: { gt: data.startTime } }
            ]
          },
          {
            AND: [
              { startTime: { lt: data.endTime } },
              { endTime: { gte: data.endTime } }
            ]
          },
          {
            AND: [
              { startTime: { gte: data.startTime } },
              { endTime: { lte: data.endTime } }
            ]
          }
        ]
      }
    });

    if (conflictingAppointment) {
      throw new Error('Tutor is not available at the requested time');
    }

    // Check if the appointment time falls within tutor's availability
    const dayOfWeek = data.startTime.getDay();
    const startTimeStr = data.startTime.toTimeString().substring(0, 5); // HH:MM format
    const endTimeStr = data.endTime.toTimeString().substring(0, 5);

    const availability = await prisma.tutorAvailability.findFirst({
      where: {
        tutorId: data.tutorId,
        dayOfWeek,
        startTime: { lte: startTimeStr },
        endTime: { gte: endTimeStr }
      }
    });

    if (!availability) {
      throw new Error('Appointment time is outside tutor\'s available hours');
    }

    // Validate meeting link for online meetings
    if (data.meetingType === 'online' && !data.meetingLink) {
      throw new Error('Meeting link is required for online appointments');
    }

    return await prisma.appointment.create({
      data: {
        tutorId: data.tutorId,
        studentId: data.studentId,
        startTime: data.startTime,
        endTime: data.endTime,
        meetingType: data.meetingType,
        meetingLink: data.meetingLink,
        notes: data.notes,
        status: 'scheduled'
      }
    });
  }

  /**
   * Get appointment by ID with details
   */
  static async getAppointmentById(appointmentId: string): Promise<AppointmentWithDetails | null> {
    return await prisma.appointment.findUnique({
      where: { id: appointmentId },
      include: {
        tutor: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true
              }
            }
          }
        },
        student: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        },
        review: {
          select: {
            id: true,
            rating: true,
            comment: true
          }
        }
      }
    });
  }

  /**
   * Get appointments for a tutor
   */
  static async getTutorAppointments(
    tutorId: string,
    options: {
      status?: string;
      page?: number;
      limit?: number;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<{
    appointments: AppointmentWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { status, page = 1, limit = 10, startDate, endDate } = options;
    const skip = (page - 1) * limit;

    const where: any = { tutorId };

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime.gte = startDate;
      }
      if (endDate) {
        where.startTime.lte = endDate;
      }
    }

    const [appointments, total] = await Promise.all([
      prisma.appointment.findMany({
        where,
        skip,
        take: limit,
        include: {
          tutor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          },
          student: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          review: {
            select: {
              id: true,
              rating: true,
              comment: true
            }
          }
        },
        orderBy: { startTime: 'desc' }
      }),
      prisma.appointment.count({ where })
    ]);

    return {
      appointments,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Get appointments for a student
   */
  static async getStudentAppointments(
    studentId: string,
    options: {
      status?: string;
      page?: number;
      limit?: number;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<{
    appointments: AppointmentWithDetails[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { status, page = 1, limit = 10, startDate, endDate } = options;
    const skip = (page - 1) * limit;

    const where: any = { studentId };

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime.gte = startDate;
      }
      if (endDate) {
        where.startTime.lte = endDate;
      }
    }

    const [appointments, total] = await Promise.all([
      prisma.appointment.findMany({
        where,
        skip,
        take: limit,
        include: {
          tutor: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  avatar: true
                }
              }
            }
          },
          student: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          review: {
            select: {
              id: true,
              rating: true,
              comment: true
            }
          }
        },
        orderBy: { startTime: 'desc' }
      }),
      prisma.appointment.count({ where })
    ]);

    return {
      appointments,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * Update appointment
   */
  static async updateAppointment(
    appointmentId: string,
    data: UpdateAppointmentData
  ): Promise<Appointment> {
    const existing = await prisma.appointment.findUnique({
      where: { id: appointmentId }
    });

    if (!existing) {
      throw new Error('Appointment not found');
    }

    // Validate time changes
    if (data.startTime || data.endTime) {
      const startTime = data.startTime || existing.startTime;
      const endTime = data.endTime || existing.endTime;

      if (startTime >= endTime) {
        throw new Error('Start time must be before end time');
      }

      // Only allow future appointments to be rescheduled
      if (startTime <= new Date()) {
        throw new Error('Cannot reschedule to past time');
      }

      // Check for conflicts if time is being changed
      if (data.startTime || data.endTime) {
        const conflictingAppointment = await prisma.appointment.findFirst({
          where: {
            tutorId: existing.tutorId,
            id: { not: appointmentId },
            status: { in: ['scheduled'] },
            OR: [
              {
                AND: [
                  { startTime: { lte: startTime } },
                  { endTime: { gt: startTime } }
                ]
              },
              {
                AND: [
                  { startTime: { lt: endTime } },
                  { endTime: { gte: endTime } }
                ]
              },
              {
                AND: [
                  { startTime: { gte: startTime } },
                  { endTime: { lte: endTime } }
                ]
              }
            ]
          }
        });

        if (conflictingAppointment) {
          throw new Error('Tutor is not available at the requested time');
        }
      }
    }

    // Validate meeting link for online meetings
    if (data.meetingType === 'online' && data.meetingLink === '') {
      throw new Error('Meeting link is required for online appointments');
    }

    return await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        ...data,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Cancel appointment
   */
  static async cancelAppointment(appointmentId: string): Promise<Appointment> {
    const existing = await prisma.appointment.findUnique({
      where: { id: appointmentId }
    });

    if (!existing) {
      throw new Error('Appointment not found');
    }

    if (existing.status === 'cancelled') {
      throw new Error('Appointment is already cancelled');
    }

    if (existing.status === 'completed') {
      throw new Error('Cannot cancel completed appointment');
    }

    return await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        status: 'cancelled',
        updatedAt: new Date()
      }
    });
  }

  /**
   * Complete appointment
   */
  static async completeAppointment(appointmentId: string): Promise<Appointment> {
    const existing = await prisma.appointment.findUnique({
      where: { id: appointmentId }
    });

    if (!existing) {
      throw new Error('Appointment not found');
    }

    if (existing.status === 'completed') {
      throw new Error('Appointment is already completed');
    }

    if (existing.status === 'cancelled') {
      throw new Error('Cannot complete cancelled appointment');
    }

    return await prisma.appointment.update({
      where: { id: appointmentId },
      data: {
        status: 'completed',
        updatedAt: new Date()
      }
    });
  }

  /**
   * Get available time slots for a tutor on a specific date
   */
  static async getAvailableTimeSlots(
    tutorId: string,
    date: Date,
    duration: number = 60 // duration in minutes
  ): Promise<{ startTime: string; endTime: string }[]> {
    const dayOfWeek = date.getDay();
    
    // Get tutor's availability for the day
    const availability = await prisma.tutorAvailability.findMany({
      where: {
        tutorId,
        dayOfWeek
      },
      orderBy: { startTime: 'asc' }
    });

    if (availability.length === 0) {
      return [];
    }

    // Get existing appointments for the date
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);

    const existingAppointments = await prisma.appointment.findMany({
      where: {
        tutorId,
        status: 'scheduled',
        startTime: {
          gte: startOfDay,
          lte: endOfDay
        }
      },
      orderBy: { startTime: 'asc' }
    });

    const availableSlots: { startTime: string; endTime: string }[] = [];

    for (const slot of availability) {
      const slotStart = this.parseTimeString(slot.startTime);
      const slotEnd = this.parseTimeString(slot.endTime);
      
      // Generate time slots within this availability window
      let currentTime = slotStart;
      
      while (currentTime + duration <= slotEnd) {
        const slotStartTime = this.formatTime(currentTime);
        const slotEndTime = this.formatTime(currentTime + duration);
        
        // Check if this slot conflicts with existing appointments
        const hasConflict = existingAppointments.some(appointment => {
          const appointmentStart = appointment.startTime.getHours() * 60 + appointment.startTime.getMinutes();
          const appointmentEnd = appointment.endTime.getHours() * 60 + appointment.endTime.getMinutes();
          
          return (currentTime < appointmentEnd && currentTime + duration > appointmentStart);
        });
        
        if (!hasConflict) {
          availableSlots.push({
            startTime: slotStartTime,
            endTime: slotEndTime
          });
        }
        
        currentTime += duration;
      }
    }

    return availableSlots;
  }

  /**
   * Parse time string (HH:MM) to minutes since midnight
   */
  private static parseTimeString(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Format minutes since midnight to time string (HH:MM)
   */
  private static formatTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}

#!/bin/bash
set -e

echo "🚀 Setting up Fastify + Prisma + TypeScript project"

# Copy .env.example to .env if it doesn't exist
if [ ! -f .env ]; then
    echo "📄 Creating .env file from .env.example"
    cp .env.example .env
    echo "✅ Created .env file"
else
    echo "ℹ️  .env file already exists, skipping..."
fi

echo "📦 Installing dependencies..."
pnpm install

echo "🔧 Setting up database..."

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
    echo "❌ PostgreSQL is not installed. Please install PostgreSQL and try again."
    echo "   On Ubuntu/Debian: sudo apt-get install postgresql postgresql-contrib"
    echo "   On macOS: brew install postgresql"
    exit 1
fi

# Check if database exists
DB_EXISTS=$(psql -tAc "SELECT 1 FROM pg_database WHERE datname='fastify_prisma'")

if [ "$DB_EXISTS" != "1" ]; then
    echo "🔄 Creating database 'fastify_prisma'..."
    createdb fastify_prisma
    echo "✅ Database created successfully"
else
    echo "ℹ️  Database 'fastify_prisma' already exists, skipping..."
fi

echo "🚀 Running database migrations..."
npx prisma migrate dev --name init

echo "✨ Setup complete! You can now start the development server with:"
echo "   pnpm dev"
